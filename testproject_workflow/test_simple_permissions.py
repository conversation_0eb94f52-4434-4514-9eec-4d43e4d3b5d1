#!/usr/bin/env python3
"""
Simple test script to demonstrate permission system functionality
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_token(username, password):
    """Get authentication token"""
    auth_data = {"username": username, "password": password}
    response = requests.post(f"{BASE_URL}/auth/token", data=auth_data)
    if response.status_code == 200:
        return response.json().get("access_token")
    return None

def test_endpoint(endpoint, token, description):
    """Test an endpoint with given token"""
    headers = {"Authorization": f"Bearer {token}"}
    try:
        response = requests.get(f"{API_BASE}{endpoint}", headers=headers)
        status_emoji = "✅" if response.status_code == 200 else "⚠️" if response.status_code == 403 else "❌"
        print(f"  {description}: {response.status_code} {status_emoji}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    Items: {data.get('total', 'N/A')}")
        elif response.status_code == 403:
            print(f"    Permission denied (expected for regular users)")
        
        return response.status_code
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 500

def main():
    print("🚀 FABI+ Permission System Test")
    print("=" * 50)
    
    # Get tokens
    admin_token = get_token("admin", "admin123")
    user_token = get_token("testuser", "testpass123")
    
    if not admin_token or not user_token:
        print("❌ Failed to get authentication tokens")
        return
    
    print("✅ Authentication tokens obtained")
    
    # Test permission endpoints
    endpoints = [
        ("/role", "Roles"),
        ("/user", "Users"),
        ("/userpermission", "User Permissions"),
        ("/grouppermission", "Group Permissions"),
        ("/rolepermission", "Role Permissions"),
        ("/modelpermission", "Model Permissions"),
        ("/fieldpermission", "Field Permissions"),
        ("/rowpermission", "Row Permissions"),
        ("/permissiontemplate", "Permission Templates")
    ]
    
    print("\n=== Permission System Test Results ===")
    
    for endpoint, name in endpoints:
        print(f"\n{name}:")
        admin_status = test_endpoint(endpoint, admin_token, "Admin access")
        user_status = test_endpoint(endpoint, user_token, "Regular user access")
        
        # Verify expected behavior
        if admin_status == 200 and user_status == 403:
            print(f"  🎉 Permission system working correctly!")
        elif admin_status != 200:
            print(f"  ⚠️  Admin should have access")
        elif user_status == 200:
            print(f"  ⚠️  Regular user should not have access")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")
    
    # Test current user endpoint
    print("\n=== Current User Information ===")
    
    for token, user_type in [(admin_token, "Admin"), (user_token, "Regular User")]:
        headers = {"Authorization": f"Bearer {token}"}
        try:
            response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
            if response.status_code == 200:
                user_data = response.json()
                print(f"\n{user_type}:")
                print(f"  Username: {user_data.get('username')}")
                print(f"  Email: {user_data.get('email')}")
                print(f"  Is staff: {user_data.get('is_staff')}")
                print(f"  Is superuser: {user_data.get('is_superuser')}")
            else:
                print(f"❌ Failed to get {user_type} info: {response.status_code}")
        except Exception as e:
            print(f"❌ Error getting {user_type} info: {e}")

if __name__ == "__main__":
    main()
