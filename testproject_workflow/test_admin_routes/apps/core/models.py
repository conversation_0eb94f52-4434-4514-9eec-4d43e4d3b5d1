"""
Core Models
Database models for the core app (SQLModel backend)
"""

from typing import Optional
from sqlmodel import Field
from fabiplus.core.models import BaseModel, register_model


# Add your models here
# Example:
# @register_model
# class MyModel(BaseModel, table=True):
#     """Example model"""
#
#     name: str = Field(max_length=100, description="Name")
#     description: Optional[str] = Field(default="", description="Description")
#     is_active: bool = Field(default=True, description="Is active")
#
#     class Config:
#         _verbose_name = "My Model"
#         _verbose_name_plural = "My Models"
#
#     def __str__(self):
#         return self.name