"""Initial_migration

Revision ID: d7d532569143
Revises: 
Create Date: 2025-07-15 13:35:14.421520+00:00

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel
import fabiplus.core.user_model


# revision identifiers, used by Alembic.
revision = "d7d532569143"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "category",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.<PERSON>ey<PERSON>onstraint("id"),
    )
    op.create_table(
        "group_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "permission_name",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column(
            "scope",
            sa.Enum(
                "GLOBAL",
                "MODEL",
                "FIELD",
                "ROW",
                "CUSTOM",
                name="permissionscope",
            ),
            nullable=False,
        ),
        sa.Column(
            "action",
            sa.Enum(
                "CREATE",
                "READ",
                "UPDATE",
                "DELETE",
                "LIST",
                "EXECUTE",
                "ADMIN",
                name="permissionaction",
            ),
            nullable=False,
        ),
        sa.Column(
            "resource", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "permission_audit_logs",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "action", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "permission_type",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column(
            "permission_id", sqlmodel.sql.sqltypes.GUID(), nullable=True
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column(
            "admin_user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True
        ),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column(
            "ip_address", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "user_agent", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "old_values", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "new_values", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "permission_templates",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "permissions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "applies_to", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_system", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "post",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "content", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("is_published", sa.Boolean(), nullable=False),
        sa.Column("author", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "roles",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_system", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "users",
        sa.Column("id", fabiplus.core.user_model.GUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column(
            "username", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("email", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "first_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "last_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_staff", sa.Boolean(), nullable=False),
        sa.Column("is_superuser", sa.Boolean(), nullable=False),
        sa.Column(
            "hashed_password",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column("last_login", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_index(
        op.f("ix_users_username"), "users", ["username"], unique=True
    )
    op.create_table(
        "activities",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column(
            "activity_type",
            sa.Enum(
                "CREATE",
                "READ",
                "UPDATE",
                "DELETE",
                "LOGIN",
                "LOGOUT",
                "ADMIN_ACCESS",
                "API_CALL",
                "ERROR",
                "SYSTEM",
                name="activitytype",
            ),
            nullable=False,
        ),
        sa.Column(
            "level",
            sa.Enum("LOW", "NORMAL", "HIGH", "CRITICAL", name="activitylevel"),
            nullable=False,
        ),
        sa.Column(
            "action", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column(
            "user_email", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "user_ip", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "user_agent", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_type", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_repr", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("method", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("path", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("status_code", sa.Integer(), nullable=True),
        sa.Column("response_time", sa.Float(), nullable=True),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_activities_action"), "activities", ["action"], unique=False
    )
    op.create_index(
        op.f("ix_activities_activity_type"),
        "activities",
        ["activity_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_level"), "activities", ["level"], unique=False
    )
    op.create_index(
        op.f("ix_activities_object_id"),
        "activities",
        ["object_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_object_type"),
        "activities",
        ["object_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_status_code"),
        "activities",
        ["status_code"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_timestamp"),
        "activities",
        ["timestamp"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_user_id"), "activities", ["user_id"], unique=False
    )
    op.create_table(
        "field_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "model_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "field_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("role_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("can_read", sa.Boolean(), nullable=False),
        sa.Column("can_write", sa.Boolean(), nullable=False),
        sa.Column("can_admin", sa.Boolean(), nullable=False),
        sa.Column("is_sensitive", sa.Boolean(), nullable=False),
        sa.Column("mask_value", sa.Boolean(), nullable=False),
        sa.Column(
            "default_value", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "model_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "model_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("role_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("can_create", sa.Boolean(), nullable=False),
        sa.Column("can_read", sa.Boolean(), nullable=False),
        sa.Column("can_update", sa.Boolean(), nullable=False),
        sa.Column("can_delete", sa.Boolean(), nullable=False),
        sa.Column("can_list", sa.Boolean(), nullable=False),
        sa.Column("can_admin", sa.Boolean(), nullable=False),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "role_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("role_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "permission_name",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column(
            "scope",
            sa.Enum(
                "GLOBAL",
                "MODEL",
                "FIELD",
                "ROW",
                "CUSTOM",
                name="permissionscope",
            ),
            nullable=False,
        ),
        sa.Column(
            "action",
            sa.Enum(
                "CREATE",
                "READ",
                "UPDATE",
                "DELETE",
                "LIST",
                "EXECUTE",
                "ADMIN",
                name="permissionaction",
            ),
            nullable=False,
        ),
        sa.Column(
            "resource", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "row_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "model_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "row_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("role_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("can_read", sa.Boolean(), nullable=False),
        sa.Column("can_update", sa.Boolean(), nullable=False),
        sa.Column("can_delete", sa.Boolean(), nullable=False),
        sa.Column("can_admin", sa.Boolean(), nullable=False),
        sa.Column("is_owner", sa.Boolean(), nullable=False),
        sa.Column(
            "owner_field", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "user_permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column(
            "permission_name",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column(
            "scope",
            sa.Enum(
                "GLOBAL",
                "MODEL",
                "FIELD",
                "ROW",
                "CUSTOM",
                name="permissionscope",
            ),
            nullable=False,
        ),
        sa.Column(
            "action",
            sa.Enum(
                "CREATE",
                "READ",
                "UPDATE",
                "DELETE",
                "LIST",
                "EXECUTE",
                "ADMIN",
                name="permissionaction",
            ),
            nullable=False,
        ),
        sa.Column(
            "resource", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "resource_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "conditions", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("granted_at", sa.DateTime(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.Column("granted_by", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("user_permissions")
    op.drop_table("row_permissions")
    op.drop_table("role_permissions")
    op.drop_table("model_permissions")
    op.drop_table("field_permissions")
    op.drop_index(op.f("ix_activities_user_id"), table_name="activities")
    op.drop_index(op.f("ix_activities_timestamp"), table_name="activities")
    op.drop_index(op.f("ix_activities_status_code"), table_name="activities")
    op.drop_index(op.f("ix_activities_object_type"), table_name="activities")
    op.drop_index(op.f("ix_activities_object_id"), table_name="activities")
    op.drop_index(op.f("ix_activities_level"), table_name="activities")
    op.drop_index(op.f("ix_activities_activity_type"), table_name="activities")
    op.drop_index(op.f("ix_activities_action"), table_name="activities")
    op.drop_table("activities")
    op.drop_index(op.f("ix_users_username"), table_name="users")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_table("users")
    op.drop_table("roles")
    op.drop_table("post")
    op.drop_table("permission_templates")
    op.drop_table("permission_audit_logs")
    op.drop_table("group_permissions")
    op.drop_table("category")
    # ### end Alembic commands ###
