#!/usr/bin/env python3
"""
Test script for different permission levels (Model, Field, Row)
Tests the granular permission system in FABI+
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api/v1"

class PermissionLevelTester:
    def __init__(self):
        self.admin_token = None
        self.user_token = None
        
    def get_admin_token(self):
        """Get admin token for testing"""
        auth_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{BASE_URL}/auth/token", data=auth_data)
        if response.status_code == 200:
            self.admin_token = response.json().get("access_token")
            return True
        return False
    
    def get_user_token(self):
        """Get regular user token for testing"""
        auth_data = {"username": "testuser", "password": "testpass123"}
        response = requests.post(f"{BASE_URL}/auth/token", data=auth_data)
        if response.status_code == 200:
            self.user_token = response.json().get("access_token")
            return True
        return False
    
    def create_test_role(self):
        """Create a test role for permission testing"""
        print("\n=== Creating Test Role ===")
        
        headers = {"Authorization": f"Bearer {self.admin_token}", "Content-Type": "application/json"}
        role_data = {
            "name": "test_role",
            "description": "Test role for permission testing",
            "is_active": True,
            "is_system": False
        }
        
        try:
            response = requests.post(f"{API_BASE}/role", json=role_data, headers=headers)
            print(f"Create role response: {response.status_code}")
            
            if response.status_code == 201:
                role = response.json()
                print(f"✅ Test role created: {role.get('name')} (ID: {role.get('id')})")
                return role
            else:
                print(f"❌ Failed to create role: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating role: {e}")
            
        return None
    
    def test_model_permissions(self):
        """Test model-level permissions"""
        print("\n=== Testing Model-Level Permissions ===")
        
        # Test with admin user (should have access)
        admin_headers = {"Authorization": f"Bearer {self.admin_token}"}
        user_headers = {"Authorization": f"Bearer {self.user_token}"}
        
        test_endpoints = [
            ("GET", "/role", "List roles"),
            ("GET", "/user", "List users"),
            ("GET", "/modelpermission", "List model permissions")
        ]
        
        for method, endpoint, description in test_endpoints:
            print(f"\n--- Testing {description} ---")
            
            # Test with admin
            try:
                if method == "GET":
                    admin_response = requests.get(f"{API_BASE}{endpoint}", headers=admin_headers)
                    user_response = requests.get(f"{API_BASE}{endpoint}", headers=user_headers)
                
                print(f"Admin access: {admin_response.status_code} {'✅' if admin_response.status_code == 200 else '❌'}")
                print(f"User access: {user_response.status_code} {'✅ (allowed)' if user_response.status_code == 200 else '⚠️ (denied)' if user_response.status_code == 403 else '❌ (error)'}")
                
                if admin_response.status_code == 200:
                    data = admin_response.json()
                    print(f"  Admin can see {data.get('total', 0)} items")
                    
            except Exception as e:
                print(f"❌ Error testing {endpoint}: {e}")
    
    def create_model_permission(self, user_id: str, model_name: str, permissions: Dict[str, bool]):
        """Create a model permission for testing"""
        print(f"\n--- Creating Model Permission for {model_name} ---")
        
        headers = {"Authorization": f"Bearer {self.admin_token}", "Content-Type": "application/json"}
        permission_data = {
            "model_name": model_name,
            "user_id": user_id,
            "role_id": None,
            **permissions,
            "conditions": "{}",
            "is_active": True
        }
        
        try:
            response = requests.post(f"{API_BASE}/modelpermission", json=permission_data, headers=headers)
            print(f"Create model permission response: {response.status_code}")
            
            if response.status_code == 201:
                perm = response.json()
                print(f"✅ Model permission created for {model_name}")
                return perm
            else:
                print(f"❌ Failed to create model permission: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating model permission: {e}")
            
        return None
    
    def test_field_permissions(self):
        """Test field-level permissions"""
        print("\n=== Testing Field-Level Permissions ===")
        
        # This would require implementing field-level filtering in the API
        # For now, we'll test the field permission model CRUD operations
        
        headers = {"Authorization": f"Bearer {self.admin_token}", "Content-Type": "application/json"}
        
        # Create a field permission
        field_perm_data = {
            "model_name": "user",
            "field_name": "email",
            "user_id": None,  # Will be set to testuser ID
            "role_id": None,
            "can_read": True,
            "can_write": False,
            "can_admin": False,
            "is_sensitive": True,
            "mask_value": True,
            "conditions": "{}"
        }
        
        try:
            response = requests.post(f"{API_BASE}/fieldpermission", json=field_perm_data, headers=headers)
            print(f"Create field permission response: {response.status_code}")
            
            if response.status_code == 201:
                perm = response.json()
                print(f"✅ Field permission created for user.email")
                print(f"  Can read: {perm.get('can_read')}")
                print(f"  Can write: {perm.get('can_write')}")
                print(f"  Is sensitive: {perm.get('is_sensitive')}")
                return perm
            else:
                print(f"❌ Failed to create field permission: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating field permission: {e}")
            
        return None
    
    def test_row_permissions(self):
        """Test row-level permissions"""
        print("\n=== Testing Row-Level Permissions ===")
        
        headers = {"Authorization": f"Bearer {self.admin_token}", "Content-Type": "application/json"}
        
        # Create a row permission
        row_perm_data = {
            "model_name": "user",
            "row_id": "test-user-id",  # This would be a real user ID in practice
            "user_id": None,  # Will be set to testuser ID
            "role_id": None,
            "can_read": True,
            "can_update": False,
            "can_delete": False,
            "can_admin": False,
            "is_owner": False,
            "owner_field": "created_by",
            "conditions": "{}"
        }
        
        try:
            response = requests.post(f"{API_BASE}/rowpermission", json=row_perm_data, headers=headers)
            print(f"Create row permission response: {response.status_code}")
            
            if response.status_code == 201:
                perm = response.json()
                print(f"✅ Row permission created for user#{row_perm_data['row_id']}")
                print(f"  Can read: {perm.get('can_read')}")
                print(f"  Can update: {perm.get('can_update')}")
                print(f"  Can delete: {perm.get('can_delete')}")
                print(f"  Is owner: {perm.get('is_owner')}")
                return perm
            else:
                print(f"❌ Failed to create row permission: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating row permission: {e}")
            
        return None
    
    def test_permission_templates(self):
        """Test permission templates"""
        print("\n=== Testing Permission Templates ===")
        
        headers = {"Authorization": f"Bearer {self.admin_token}", "Content-Type": "application/json"}
        
        # Create a permission template
        template_data = {
            "name": "read_only_user",
            "description": "Template for read-only users",
            "permissions": json.dumps({
                "default_permissions": {
                    "can_read": True,
                    "can_create": False,
                    "can_update": False,
                    "can_delete": False,
                    "can_list": True
                }
            }),
            "applies_to": json.dumps(["user", "role", "post"]),
            "is_active": True,
            "is_system": False
        }
        
        try:
            response = requests.post(f"{API_BASE}/permissiontemplate", json=template_data, headers=headers)
            print(f"Create permission template response: {response.status_code}")
            
            if response.status_code == 201:
                template = response.json()
                print(f"✅ Permission template created: {template.get('name')}")
                print(f"  Description: {template.get('description')}")
                print(f"  Is active: {template.get('is_active')}")
                return template
            else:
                print(f"❌ Failed to create permission template: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating permission template: {e}")
            
        return None
    
    def run_all_tests(self):
        """Run all permission level tests"""
        print("🚀 Starting Permission Level Tests")
        print("=" * 50)
        
        # Get tokens
        if not self.get_admin_token():
            print("❌ Failed to get admin token")
            return
            
        if not self.get_user_token():
            print("❌ Failed to get user token")
            return
            
        print("✅ Authentication tokens obtained")
        
        # Test 1: Model-level permissions
        self.test_model_permissions()
        
        # Test 2: Create test role
        test_role = self.create_test_role()
        
        # Test 3: Field-level permissions
        self.test_field_permissions()
        
        # Test 4: Row-level permissions
        self.test_row_permissions()
        
        # Test 5: Permission templates
        self.test_permission_templates()
        
        print("\n" + "=" * 50)
        print("🏁 Permission Level Tests completed")

if __name__ == "__main__":
    tester = PermissionLevelTester()
    tester.run_all_tests()
