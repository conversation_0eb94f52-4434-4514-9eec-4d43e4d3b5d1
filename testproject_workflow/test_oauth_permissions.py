#!/usr/bin/env python3
"""
Test script for OAuth authentication and permissions system
Tests authentication, token generation, and permission checks
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api/v1"

class OAuthPermissionTester:
    def __init__(self):
        self.access_token = None
        self.current_user = None
        
    def test_unauthenticated_access(self):
        """Test that protected endpoints require authentication"""
        print("\n=== Testing Unauthenticated Access ===")
        
        # Test accessing protected endpoints without authentication
        protected_endpoints = [
            "/api/v1/role",
            "/api/v1/userpermission", 
            "/api/v1/grouppermission",
            "/api/v1/rolepermission",
            "/api/v1/modelpermission",
            "/api/v1/fieldpermission",
            "/api/v1/rowpermission",
            "/api/v1/permissiontemplate"
        ]
        
        for endpoint in protected_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}")
                print(f"GET {endpoint}: Status {response.status_code}")
                
                if response.status_code == 401:
                    print(f"  ✅ Correctly requires authentication")
                elif response.status_code == 403:
                    print(f"  ✅ Correctly denies access (forbidden)")
                else:
                    print(f"  ❌ Unexpected status code: {response.status_code}")
                    print(f"  Response: {response.text[:200]}")
                    
            except Exception as e:
                print(f"  ❌ Error testing {endpoint}: {e}")
    
    def create_test_user(self):
        """Create a test user for authentication"""
        print("\n=== Test User Setup ===")
        print("✅ Test user 'testuser' already created via CLI")
        print("✅ Admin user 'admin' already exists")
        return True
    
    def test_token_authentication(self, username="testuser", password="testpass123"):
        """Test OAuth token authentication"""
        print(f"\n=== Testing Token Authentication for {username} ===")

        # Test token endpoint
        auth_data = {
            "username": username,
            "password": password
        }

        try:
            response = requests.post(f"{BASE_URL}/auth/token", data=auth_data)
            print(f"Token request response: {response.status_code}")

            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get("access_token")
                print(f"✅ Token obtained successfully for {username}")
                print(f"Token type: {token_data.get('token_type')}")
                return True
            else:
                print(f"❌ Failed to get token: {response.text}")

        except Exception as e:
            print(f"❌ Error getting token: {e}")

        return False
    
    def test_authenticated_access(self):
        """Test accessing protected endpoints with authentication"""
        print("\n=== Testing Authenticated Access ===")
        
        if not self.access_token:
            print("❌ No access token available")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # Test accessing protected endpoints with authentication
        protected_endpoints = [
            "/api/v1/role",
            "/api/v1/userpermission", 
            "/api/v1/grouppermission",
            "/api/v1/rolepermission",
            "/api/v1/modelpermission",
            "/api/v1/fieldpermission",
            "/api/v1/rowpermission",
            "/api/v1/permissiontemplate"
        ]
        
        for endpoint in protected_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                print(f"GET {endpoint}: Status {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  ✅ Successfully accessed with authentication")
                    data = response.json()
                    print(f"  Items count: {data.get('total', 'N/A')}")
                elif response.status_code == 403:
                    print(f"  ⚠️  Authenticated but permission denied")
                elif response.status_code == 401:
                    print(f"  ❌ Authentication failed")
                else:
                    print(f"  ❌ Unexpected status: {response.status_code}")
                    print(f"  Response: {response.text[:200]}")
                    
            except Exception as e:
                print(f"  ❌ Error testing {endpoint}: {e}")
    
    def test_current_user_endpoint(self):
        """Test getting current user information"""
        print("\n=== Testing Current User Endpoint ===")
        
        if not self.access_token:
            print("❌ No access token available")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
            print(f"Current user response: {response.status_code}")
            
            if response.status_code == 200:
                user_data = response.json()
                self.current_user = user_data
                print(f"✅ Current user retrieved successfully")
                print(f"Username: {user_data.get('username')}")
                print(f"Email: {user_data.get('email')}")
                print(f"Is staff: {user_data.get('is_staff')}")
                print(f"Is superuser: {user_data.get('is_superuser')}")
            else:
                print(f"❌ Failed to get current user: {response.text}")
                
        except Exception as e:
            print(f"❌ Error getting current user: {e}")
    
    def run_all_tests(self):
        """Run all authentication and permission tests"""
        print("🚀 Starting OAuth and Permission System Tests")
        print("=" * 50)

        # Test 1: Unauthenticated access should be denied
        self.test_unauthenticated_access()

        # Test 2: Create test user (may require manual setup)
        self.create_test_user()

        # Test 3: Test token authentication for regular user
        print("\n--- Testing Regular User ---")
        if self.test_token_authentication("testuser", "testpass123"):
            # Test 4: Test authenticated access
            self.test_authenticated_access()

            # Test 5: Test current user endpoint
            self.test_current_user_endpoint()

        # Test 6: Test token authentication for admin user
        print("\n--- Testing Admin User ---")
        if self.test_token_authentication("admin", "admin123"):
            # Test 7: Test authenticated access as admin
            self.test_authenticated_access()

            # Test 8: Test current user endpoint as admin
            self.test_current_user_endpoint()

        print("\n" + "=" * 50)
        print("🏁 Tests completed")

if __name__ == "__main__":
    tester = OAuthPermissionTester()
    tester.run_all_tests()
