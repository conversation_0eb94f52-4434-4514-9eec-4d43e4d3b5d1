[tool.poetry]
name = "testproject_workflow"
version = "0.1.0"
description = "Testproject_Workflow - FABI+ API Project (SQLMODEL backend)"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "testproject_workflow"}, {include = "apps"}]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = ">=0.104.0"
"uvicorn[standard]" = ">=0.24.0"
sqlmodel = ">=0.0.14"
pydantic = ">=2.5.0"
pydantic-settings = ">=2.1.0"
sqlalchemy = ">=2.0.0"
alembic = ">=1.13.0"
"python-jose[cryptography]" = ">=3.3.0"
"passlib[bcrypt]" = ">=1.7.4"
python-multipart = ">=0.0.6"
bcrypt = ">=4.1.0"
orjson = ">=3.9.0"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
httpx = "^0.25.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[tool.poetry.extras]
postgresql = ["psycopg2-binary>=2.9.0"]
mysql = ["pymysql>=1.1.0"]
redis = ["redis>=5.0.0", "hiredis>=2.2.0"]
monitoring = ["sentry-sdk[fastapi]>=1.38.0"]
[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
known_first_party = ["testproject_workflow", "apps"]

