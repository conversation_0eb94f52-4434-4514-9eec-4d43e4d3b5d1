"""
Blog Serializers
Pydantic schemas for the blog app
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
import uuid


class BlogItemBase(BaseModel):
    """Base schema for BlogItem"""
    name: str = Field(..., max_length=100, description="Item name")
    description: Optional[str] = Field("", description="Item description")
    is_active: bool = Field(True, description="Is item active")


class BlogItemCreate(BlogItemBase):
    """Schema for creating BlogItem"""
    pass


class BlogItemUpdate(BlogItemBase):
    """Schema for updating BlogItem"""
    name: Optional[str] = Field(None, max_length=100, description="Item name")
    is_active: Optional[bool] = Field(None, description="Is item active")


class BlogItemResponse(BlogItemBase):
    """Schema for BlogItem response"""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True