"""
Blog App Configuration
Django-style app configuration for blog
"""

from fabiplus.core.apps import AppConfig


class BlogConfig(AppConfig):
    """Configuration for blog app"""
    
    name = "blog"
    verbose_name = "Blog"
    
    def ready(self):
        """App initialization"""
        # Import models to register them
        from . import models
        
        # Import admin to register admin views
        from . import admin
        
        # Any other app initialization code
        pass


# Default app config
default_app_config = "blog.apps.BlogConfig"