"""
Blog Views
API views for the blog app with authentication
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from fabiplus.core.views import GenericAPIView, AuthenticatedGenericAPIView
from fabiplus.core.auth import get_current_active_user, User
from fabiplus.core.models import ModelRegistry
from datetime import datetime
from .models import BlogPost, BlogCategory, BlogTag, BlogComment
from .staff_roles import (
    BlogRoleManager, BlogPermission, BlogRole, BlogRoleViewMixin,
    require_blog_permission, require_blog_role
)

# Create router for this app
router = APIRouter(prefix="/blog", tags=["Blog"])

class CategoryView(AuthenticatedGenericAPIView, BlogRoleViewMixin):
    """Category management view - requires authentication for CUD operations"""
    model = BlogCategory

    def list(self, session, *args, **kwargs):
        # Public endpoint - anyone can list categories
        return super().list(session, *args, **kwargs)

    def retrieve(self, item_id, session):
        # Public endpoint - anyone can view categories
        return super().retrieve(item_id, session)

    def create(self, data, session, current_user=None):
        # Requires CREATE_CATEGORY permission
        BlogRoleManager.require_permission(current_user, BlogPermission.CREATE_CATEGORY)
        return super().create(data, session, current_user=current_user)

    def update(self, item_id, data, session, current_user=None):
        # Requires EDIT_CATEGORY permission
        BlogRoleManager.require_permission(current_user, BlogPermission.EDIT_CATEGORY)
        return super().update(item_id, data, session, current_user=current_user)

    def delete(self, item_id, session, current_user=None):
        # Requires DELETE_CATEGORY permission
        BlogRoleManager.require_permission(current_user, BlogPermission.DELETE_CATEGORY)
        return super().delete(item_id, session, current_user=current_user)

class TagView(AuthenticatedGenericAPIView, BlogRoleViewMixin):
    """Tag management view - requires authentication for CUD operations"""
    model = BlogTag

    def list(self, session, *args, **kwargs):
        # Public endpoint - anyone can list tags
        return super().list(session, *args, **kwargs)

    def retrieve(self, item_id, session):
        # Public endpoint - anyone can view tags
        return super().retrieve(item_id, session)

    def create(self, data, session, current_user=None):
        # Requires CREATE_TAG permission
        BlogRoleManager.require_permission(current_user, BlogPermission.CREATE_TAG)
        return super().create(data, session, current_user=current_user)

    def update(self, item_id, data, session, current_user=None):
        # Requires EDIT_TAG permission
        BlogRoleManager.require_permission(current_user, BlogPermission.EDIT_TAG)
        return super().update(item_id, data, session, current_user=current_user)

    def delete(self, item_id, session, current_user=None):
        # Requires DELETE_TAG permission
        BlogRoleManager.require_permission(current_user, BlogPermission.DELETE_TAG)
        return super().delete(item_id, session, current_user=current_user)

class PostView(AuthenticatedGenericAPIView, BlogRoleViewMixin):
    """Post management view - requires authentication for CUD operations"""
    model = BlogPost

    def list(self, session, *args, **kwargs):
        # Public endpoint - anyone can list published posts
        kwargs['filters'] = kwargs.get('filters', {})
        kwargs['filters']['is_published'] = True
        return super().list(session, *args, **kwargs)

    def retrieve(self, item_id, session):
        # Public endpoint - anyone can view published posts
        post = session.get(BlogPost, item_id)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        if not post.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Post not found"
            )

        # Increment view count
        post.view_count += 1
        session.commit()

        return post

    def create(self, data, session, current_user=None):
        # Requires CREATE_POST permission
        BlogRoleManager.require_permission(current_user, BlogPermission.CREATE_POST)

        # Set author
        data['author_id'] = current_user.id

        # Check if user can publish posts
        if data.get('is_published'):
            if not BlogRoleManager.has_permission(current_user, BlogPermission.PUBLISH_POST):
                data['is_published'] = False  # Force to draft if no publish permission
            else:
                data['published_at'] = datetime.now(datetime.timezone.utc)

        return super().create(data, session, current_user=current_user)

    def update(self, item_id, data, session, current_user=None):
        post = session.get(BlogPost, item_id)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Check edit permissions using role manager
        self.check_post_edit_permission(current_user, post)

        # Handle publishing permissions
        if data.get('is_published') and not post.is_published:
            self.check_post_publish_permission(current_user, post)
            data['published_at'] = datetime.now(datetime.timezone.utc)
        elif not data.get('is_published'):
            data['published_at'] = None

        return super().update(item_id, data, session, current_user=current_user)

    def delete(self, item_id, session, current_user=None):
        post = session.get(BlogPost, item_id)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Check delete permissions using role manager
        self.check_post_delete_permission(current_user, post)

        return super().delete(item_id, session, current_user=current_user)

class CommentView(AuthenticatedGenericAPIView, BlogRoleViewMixin):
    """Comment management view - mixed authentication"""
    model = BlogComment

    def list(self, session, *args, **kwargs):
        # Public endpoint - anyone can list approved comments
        kwargs['filters'] = kwargs.get('filters', {})
        kwargs['filters']['is_approved'] = True
        kwargs['filters']['is_spam'] = False
        return super().list(session, *args, **kwargs)

    def create(self, data, session, current_user=None):
        # Comments can be created by authenticated users or guests
        if current_user:
            data['author_id'] = current_user.id
            data['is_approved'] = True  # Auto-approve for authenticated users
        else:
            # Guest comment - require moderation
            data['is_approved'] = False
            if not all([data.get('guest_name'), data.get('guest_email')]):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Guest name and email are required for anonymous comments"
                )

        return super().create(data, session, current_user=current_user)

    def update(self, item_id, data, session, current_user=None):
        # Requires MODERATE_COMMENTS permission
        BlogRoleManager.require_permission(current_user, BlogPermission.MODERATE_COMMENTS)
        return super().update(item_id, data, session, current_user=current_user)

    def delete(self, item_id, session, current_user=None):
        # Requires DELETE_COMMENTS permission
        BlogRoleManager.require_permission(current_user, BlogPermission.DELETE_COMMENTS)
        return super().delete(item_id, session, current_user=current_user)

# Role management endpoints
@router.get("/roles/")
async def get_available_roles(current_user: User = Depends(get_current_active_user)):
    """Get available blog roles"""
    BlogRoleManager.require_permission(current_user, BlogPermission.MANAGE_AUTHORS)

    from .staff_roles import get_available_roles
    return {"roles": get_available_roles()}

@router.post("/roles/assign/")
async def assign_role(
    user_id: str,
    role: BlogRole,
    current_user: User = Depends(get_current_active_user)
):
    """Assign blog role to user"""
    BlogRoleManager.require_permission(current_user, BlogPermission.MANAGE_AUTHORS)

    from .staff_roles import BlogRoleAssignment
    session = ModelRegistry.get_session()
    try:
        user = session.get(User, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        success = BlogRoleAssignment.assign_role(user, role)
        if success:
            return {"message": f"Role {role.value} assigned to {user.username}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to assign role")
    finally:
        session.close()

@router.delete("/roles/remove/")
async def remove_role(
    user_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Remove blog role from user"""
    BlogRoleManager.require_permission(current_user, BlogPermission.MANAGE_AUTHORS)

    from .staff_roles import BlogRoleAssignment
    session = ModelRegistry.get_session()
    try:
        user = session.get(User, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        success = BlogRoleAssignment.remove_role(user)
        if success:
            return {"message": f"Role removed from {user.username}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to remove role")
    finally:
        session.close()

# Custom endpoints
@router.get("/stats/")
async def get_blog_stats(current_user: User = Depends(get_current_active_user)):
    """Get blog statistics"""
    BlogRoleManager.require_permission(current_user, BlogPermission.VIEW_ANALYTICS)

    session = ModelRegistry.get_session()
    try:
        total_posts = session.query(BlogPost).count()
        published_posts = session.query(BlogPost).filter(BlogPost.is_published == True).count()
        total_comments = session.query(BlogComment).count()
        total_categories = session.query(BlogCategory).count()
        total_tags = session.query(BlogTag).count()

        return {
            "total_posts": total_posts,
            "published_posts": published_posts,
            "total_comments": total_comments,
            "total_categories": total_categories,
            "total_tags": total_tags
        }
    finally:
        session.close()

@router.get("/featured/")
async def get_featured_posts():
    """Get featured posts"""
    session = ModelRegistry.get_session()
    try:
        featured_posts = session.query(BlogPost).filter(
            BlogPost.is_featured == True,
            BlogPost.is_published == True
        ).limit(5).all()

        return {"featured_posts": [
            {
                "id": str(post.id),
                "title": post.title,
                "excerpt": post.excerpt,
                "slug": post.slug,
                "published_at": post.published_at.isoformat() if post.published_at else None
            }
            for post in featured_posts
        ]}
    finally:
        session.close()

@router.get("/recent/")
async def get_recent_posts():
    """Get recent posts"""
    session = ModelRegistry.get_session()
    try:
        recent_posts = session.query(BlogPost).filter(
            BlogPost.is_published == True
        ).order_by(BlogPost.published_at.desc()).limit(10).all()

        return {"recent_posts": [
            {
                "id": str(post.id),
                "title": post.title,
                "excerpt": post.excerpt,
                "slug": post.slug,
                "published_at": post.published_at.isoformat() if post.published_at else None
            }
            for post in recent_posts
        ]}
    finally:
        session.close()