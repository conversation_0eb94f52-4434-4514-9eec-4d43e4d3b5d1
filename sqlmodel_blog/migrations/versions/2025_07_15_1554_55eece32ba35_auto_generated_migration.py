"""Auto-generated migration

Revision ID: 55eece32ba35
Revises: f3a66b854be1
Create Date: 2025-07-15 15:54:51.207333+00:00

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op

import fabiplus.core.user_model

# revision identifiers, used by Alembic.
revision = "55eece32ba35"
down_revision = "f3a66b854be1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "blog_categories",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("slug", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_blog_categories_name"),
        "blog_categories",
        ["name"],
        unique=True,
    )
    op.create_index(
        op.f("ix_blog_categories_slug"),
        "blog_categories",
        ["slug"],
        unique=True,
    )
    op.create_table(
        "blog_tags",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("slug", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("color", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_blog_tags_name"), "blog_tags", ["name"], unique=True)
    op.create_index(op.f("ix_blog_tags_slug"), "blog_tags", ["slug"], unique=True)
    op.create_table(
        "blog_posts",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("content", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("excerpt", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("slug", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("is_published", sa.Boolean(), nullable=False),
        sa.Column("is_featured", sa.Boolean(), nullable=False),
        sa.Column("view_count", sa.Integer(), nullable=False),
        sa.Column("author_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("category_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("published_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["author_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["category_id"],
            ["blog_categories.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_blog_posts_slug"), "blog_posts", ["slug"], unique=True)
    op.create_index(op.f("ix_blog_posts_title"), "blog_posts", ["title"], unique=False)
    op.create_table(
        "blog_comments",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("content", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("is_approved", sa.Boolean(), nullable=False),
        sa.Column("is_spam", sa.Boolean(), nullable=False),
        sa.Column("post_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("author_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("parent_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column("guest_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("guest_email", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("guest_website", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["author_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_id"],
            ["blog_comments.id"],
        ),
        sa.ForeignKeyConstraint(
            ["post_id"],
            ["blog_posts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "blog_post_tags",
        sa.Column("post_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("tag_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["post_id"],
            ["blog_posts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tag_id"],
            ["blog_tags.id"],
        ),
        sa.PrimaryKeyConstraint("post_id", "tag_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("blog_post_tags")
    op.drop_table("blog_comments")
    op.drop_index(op.f("ix_blog_posts_title"), table_name="blog_posts")
    op.drop_index(op.f("ix_blog_posts_slug"), table_name="blog_posts")
    op.drop_table("blog_posts")
    op.drop_index(op.f("ix_blog_tags_slug"), table_name="blog_tags")
    op.drop_index(op.f("ix_blog_tags_name"), table_name="blog_tags")
    op.drop_table("blog_tags")
    op.drop_index(op.f("ix_blog_categories_slug"), table_name="blog_categories")
    op.drop_index(op.f("ix_blog_categories_name"), table_name="blog_categories")
    op.drop_table("blog_categories")
    # ### end Alembic commands ###
