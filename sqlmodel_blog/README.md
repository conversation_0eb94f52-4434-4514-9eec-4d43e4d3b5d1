# Sqlmodel_Blog

Sqlmodel_Blog - A FABI+ API Project

## Quick Start

1. Install dependencies:
   ```bash
   poetry install
   ```

2. Setup environment:
   ```bash
   cp .env.example .env
   ```

3. Run migrations:
   ```bash
   poetry run python manage.py db migrate
   ```

4. Create superuser:
   ```bash
   poetry run python manage.py user create
   ```

5. Start development server:
   ```bash
   poetry run python manage.py server run
   ```

6. Visit your API:
   - API Documentation: http://localhost:8000/docs
   - Admin Interface: http://localhost:8000/admin

## Project Structure

```
sqlmodel_blog/
├── sqlmodel_blog/          # Main project package
│   ├── settings.py      # Project settings
│   ├── urls.py          # URL configuration
│   └── wsgi.py          # WSGI application
├── apps/                # Project apps
│   └── core/            # Core app
├── manage.py            # Management script
├── pyproject.toml       # Poetry configuration
└── README.md            # This file
```

## Development

- Create new app: `poetry run python manage.py app startapp myapp`
- Run tests: `poetry run pytest`
- Format code: `poetry run black .`

## Deployment

See deployment documentation for production setup.