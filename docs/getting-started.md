# 🚀 FABI+ Framework - Complete Getting Started Guide

Welcome to FABI+! This comprehensive guide will walk you through every feature of the framework, from project creation to advanced functionality testing.

## 📋 Table of Contents

1. [Installation & Setup](#installation--setup)
2. [Project Creation with ORM Choice](#project-creation-with-orm-choice)
3. [Creating Your First App](#creating-your-first-app)
4. [Database Setup & Migrations](#database-setup--migrations)
5. [Media System Tutorial](#media-system-tutorial)
6. [Permissions System Tutorial](#permissions-system-tutorial)
7. [Response Types & Streaming](#response-types--streaming)
8. [Admin Interface](#admin-interface)
9. [Testing Everything](#testing-everything)
10. [Real-World Examples](#real-world-examples)

---

## 🔧 Installation & Setup

### Prerequisites
- Python 3.10+
- Poetry (recommended) or pip

### Install FABI+

```bash
# Clone the repository
git clone https://github.com/Helevon-Technologies-LTD/fabiplus.git
cd fabiplus

# Install with Poetry (recommended)
poetry install

# Or install with pip
pip install -e .

# Verify installation
fabiplus --help
```

### Available Commands

```bash
# Project management
fabiplus project startproject myproject    # Create new project
fabiplus project list-templates           # List project templates
fabiplus project list-orms               # List available ORMs

# App management
fabiplus app startapp myapp               # Create new app
fabiplus app addmodel MyModel             # Add model to app

# Server management
fabiplus server run                       # Development server
fabiplus server run --production          # Production server
fabiplus server status                    # Check server status

# Cache management
fabiplus cache clear                      # Clear cache
fabiplus cache list                       # List cached data
```

---

## 🗄️ Project Creation with ORM Choice

FABI+ supports **three different ORMs**. Let's create projects with each:

### 1. SQLModel Project (Default - Recommended)

```bash
# Create project with SQLModel (default)
fabiplus project startproject blog_sqlmodel --orm sqlmodel

# Or simply (SQLModel is default)
fabiplus project startproject blog_sqlmodel
```

**Features:**
- ✅ Type safety with Pydantic
- ✅ Async support
- ✅ FastAPI integration
- ✅ SQLAlchemy power under the hood

### 2. Pure SQLAlchemy Project

```bash
# Create project with pure SQLAlchemy
fabiplus project startproject blog_sqlalchemy --orm sqlalchemy
```

**Features:**
- ✅ Full SQLAlchemy power
- ✅ Mature ecosystem
- ✅ Complex query support
- ✅ Async support (SQLAlchemy 2.0+)

### 3. Tortoise ORM Project (Async-First)

```bash
# Create project with Tortoise ORM
fabiplus project startproject blog_tortoise --orm tortoise
```

**Features:**
- ✅ Async-first design
- ✅ Django-like syntax
- ✅ Built-in migrations (Aerich)
- ✅ High performance

### 4. Project with Docker Support

```bash
# Add Docker files to any project
fabiplus project startproject blog_docker --orm sqlmodel --docker
```

### Exploring Project Structure

```bash
cd blog_sqlmodel
tree .
```

```
blog_sqlmodel/
├── blog_sqlmodel/
│   ├── __init__.py
│   ├── settings.py          # Project settings
│   ├── urls.py             # URL routing
│   └── wsgi.py             # WSGI application
├── apps/
│   └── core/               # Default core app
│       ├── __init__.py
│       ├── models.py       # Database models
│       ├── views.py        # API views
│       ├── admin.py        # Admin configuration
│       └── urls.py         # App URLs
├── migrations/             # Database migrations
├── media/                  # Media files storage
├── static/                 # Static files
├── templates/              # Jinja2 templates
├── pyproject.toml          # Dependencies (ORM-specific)
├── alembic.ini            # Migration config (SQLModel/SQLAlchemy)
├── README.md
└── manage.py              # Management script

---

## 📱 Creating Your First App

Let's create a complete blog application to showcase all features:

### 1. Navigate to Your Project

```bash
cd blog_sqlmodel
```

### 2. Create a Blog App

```bash
# Create blog app
fabiplus app startapp blog

# Check the created structure
tree apps/blog/
```

```
apps/blog/
├── __init__.py
├── models.py          # Blog models
├── views.py           # API endpoints
├── admin.py           # Admin configuration
├── urls.py            # URL routing
├── serializers.py     # Data serialization
└── tests.py           # Unit tests
```

### 3. Create Models with Different ORMs

#### SQLModel Models (blog_sqlmodel/apps/blog/models.py)

```python
"""
Blog models with SQLModel
"""

from typing import Optional, List
from datetime import datetime
from sqlmodel import SQLModel, Field, Relationship
from fabiplus.core.models import BaseModel, register_model


@register_model
class Category(BaseModel, table=True):
    """Blog category model"""
    __tablename__ = "blog_categories"

    name: str = Field(max_length=100, description="Category name")
    slug: str = Field(max_length=100, unique=True, description="URL slug")
    description: Optional[str] = Field(default="", description="Category description")
    is_active: bool = Field(default=True, description="Is category active")

    # Relationships
    posts: List["Post"] = Relationship(back_populates="category")

    class Config:
        _verbose_name = "Category"
        _verbose_name_plural = "Categories"

    def __str__(self):
        return self.name


@register_model
class Tag(BaseModel, table=True):
    """Blog tag model"""
    __tablename__ = "blog_tags"

    name: str = Field(max_length=50, description="Tag name")
    color: Optional[str] = Field(default="#007bff", description="Tag color")

    class Config:
        _verbose_name = "Tag"
        _verbose_name_plural = "Tags"

    def __str__(self):
        return self.name


@register_model
class Post(BaseModel, table=True):
    """Blog post model"""
    __tablename__ = "blog_posts"

    title: str = Field(max_length=200, description="Post title")
    slug: str = Field(max_length=200, unique=True, description="URL slug")
    content: str = Field(description="Post content")
    excerpt: Optional[str] = Field(default="", description="Post excerpt")

    # Status
    is_published: bool = Field(default=False, description="Is post published")
    is_featured: bool = Field(default=False, description="Is post featured")

    # SEO
    meta_title: Optional[str] = Field(default="", max_length=60)
    meta_description: Optional[str] = Field(default="", max_length=160)

    # Timestamps
    published_at: Optional[datetime] = Field(default=None)

    # Foreign Keys
    category_id: Optional[int] = Field(default=None, foreign_key="blog_categories.id")
    author_id: Optional[int] = Field(default=None, foreign_key="users.id")

    # Relationships
    category: Optional[Category] = Relationship(back_populates="posts")

    class Config:
        _verbose_name = "Post"
        _verbose_name_plural = "Posts"

    def __str__(self):
        return self.title
```

#### Add the Models to Your Project

```bash
# Add models using CLI
fabiplus app addmodel Category --app blog --fields "name:str,slug:str,description:text,is_active:bool"
fabiplus app addmodel Post --app blog --fields "title:str,slug:str,content:text,is_published:bool"
fabiplus app addmodel Tag --app blog --fields "name:str,color:str"
```

### 4. Register the App

Edit `blog_sqlmodel/settings.py`:

```python
# Add to INSTALLED_APPS
INSTALLED_APPS = [
    "apps.core",
    "apps.blog",  # Add this line
]

---

## 🗃️ Database Setup & Migrations

### 1. Initialize Database

```bash
# For SQLModel/SQLAlchemy projects
alembic init migrations

# For Tortoise projects
aerich init -t blog_tortoise.settings.TORTOISE_ORM

# Create initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### 2. Create and Apply Migrations

```bash
# After adding/changing models
alembic revision --autogenerate -m "Add blog models"
alembic upgrade head

# Check migration status
alembic current
alembic history
```

### 3. Start the Development Server

```bash
# Start server
fabiplus server run

# Or with custom host/port
fabiplus server run --host 0.0.0.0 --port 8080

# Production mode
fabiplus server run --production
```

Visit: http://localhost:8000

### 4. Test Basic API

```bash
# Test API endpoints
curl http://localhost:8000/api/v1/blog/posts/
curl http://localhost:8000/api/v1/blog/categories/

# API documentation
open http://localhost:8000/docs
```

---

## 📁 Media System Tutorial

Let's implement a complete media system for our blog:

### 1. Configure Media Settings

Edit `blog_sqlmodel/settings.py`:

```python
# Media Configuration
MEDIA_ROOT = "media"
MEDIA_URL = "/media/"

# File Upload Settings
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp',  # Images
    '.pdf', '.doc', '.docx', '.txt',           # Documents
    '.mp4', '.avi', '.mov',                    # Videos
    '.mp3', '.wav', '.flac'                    # Audio
]

# Thumbnail Settings
THUMBNAIL_SIZES = [(150, 150), (300, 300), (600, 600)]
```

### 2. Add Media Models to Blog

Edit `apps/blog/models.py`:

```python
from fabiplus.core.media.models import MediaFile

# Add to Post model
class Post(BaseModel, table=True):
    # ... existing fields ...

    # Media fields
    featured_image_id: Optional[int] = Field(default=None, foreign_key="media_files.id")
    gallery_images: Optional[List[str]] = Field(default=[], description="Gallery image IDs")

    # Relationships
    featured_image: Optional[MediaFile] = Relationship()
```

### 3. Create Media Upload Endpoint

Edit `apps/blog/views.py`:

```python
from fastapi import APIRouter, UploadFile, File, Form, Depends
from fabiplus.core.media.handlers import FileUploadHandler
from fabiplus.core.media.storage import LocalFileStorage, StorageConfig
from fabiplus.core.media.validators import get_validator_for_file
from fabiplus.core.media.processors import ImageProcessor

router = APIRouter(prefix="/blog", tags=["Blog"])

@router.post("/upload-image")
async def upload_blog_image(
    file: UploadFile = File(...),
    alt_text: str = Form(""),
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Upload image for blog post"""

    # Setup storage and handlers
    storage_config = StorageConfig(base_path="media/blog")
    storage = LocalFileStorage(storage_config)
    validator = get_validator_for_file(file.filename)
    processor = ImageProcessor()

    upload_handler = FileUploadHandler(storage, validator, processor)

    # Upload file
    media_file = await upload_handler.upload_single_file(
        file=file,
        alt_text=alt_text,
        user_id=str(current_user.id),
        session=session
    )

    return {
        "id": media_file.id,
        "url": media_file.storage_url,
        "thumbnails": media_file.thumbnails,
        "size": media_file.size,
        "content_type": media_file.content_type
    }

@router.post("/upload-multiple")
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Upload multiple images for gallery"""

    storage_config = StorageConfig(base_path="media/blog/gallery")
    storage = LocalFileStorage(storage_config)
    validator = get_validator_for_file("image.jpg")  # Image validator
    processor = ImageProcessor()

    upload_handler = FileUploadHandler(storage, validator, processor)

    # Upload files
    uploaded_files = await upload_handler.upload_multiple_files(
        files=files,
        user_id=str(current_user.id),
        session=session
    )

    return {
        "uploaded_count": len(uploaded_files),
        "files": [
            {
                "id": f.id,
                "url": f.storage_url,
                "thumbnails": f.thumbnails
            }
            for f in uploaded_files
        ]
    }

### 4. Test Media Upload

```bash
# Test single image upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test-image.jpg" \
  -F "alt_text=Test blog image"

# Test multiple upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-multiple" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@image1.jpg" \
  -F "files=@image2.jpg" \
  -F "files=@image3.jpg"
```

### 5. Chunked Upload for Large Files

```python
@router.post("/upload-large/start")
async def start_large_upload(
    filename: str = Form(...),
    total_size: int = Form(...),
    chunk_size: int = Form(1024*1024),  # 1MB chunks
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Start chunked upload for large files"""

    storage_config = StorageConfig(base_path="media/blog/large")
    storage = LocalFileStorage(storage_config)
    validator = get_validator_for_file(filename)
    processor = ImageProcessor()

    upload_handler = FileUploadHandler(storage, validator, processor)

    upload_session = await upload_handler.start_chunked_upload(
        filename=filename,
        total_size=total_size,
        chunk_size=chunk_size,
        user_id=str(current_user.id),
        session=session
    )

    return {
        "session_id": upload_session.session_id,
        "total_chunks": upload_session.total_chunks,
        "chunk_size": upload_session.chunk_size
    }

@router.post("/upload-large/{session_id}/chunk/{chunk_number}")
async def upload_chunk(
    session_id: str,
    chunk_number: int,
    chunk: UploadFile = File(...),
    session: Session = Depends(get_session)
):
    """Upload a file chunk"""

    storage_config = StorageConfig(base_path="media/blog/large")
    storage = LocalFileStorage(storage_config)
    validator = get_validator_for_file("file.bin")
    processor = ImageProcessor()

    upload_handler = FileUploadHandler(storage, validator, processor)

    chunk_data = await chunk.read()

    result = await upload_handler.upload_chunk(
        session_id=session_id,
        chunk_number=chunk_number,
        chunk_data=chunk_data,
        session=session
    )

    return result
```

---

## 🔐 Permissions System Tutorial

Let's implement comprehensive permissions for our blog:

### 1. Create Permission Models

```bash
# Create permissions migration
alembic revision --autogenerate -m "Add permission models"
alembic upgrade head
```

### 2. Setup Blog Permissions

Edit `apps/blog/permissions.py`:

```python
from fabiplus.core.permissions.base import Permission, PermissionAction, PermissionScope
from fabiplus.core.permissions.models import ModelPermission, FieldPermission, RowPermission

# Define blog permissions
BLOG_PERMISSIONS = [
    # Model-level permissions
    Permission(
        name="blog.view_post",
        scope=PermissionScope.MODEL,
        action=PermissionAction.READ,
        resource="Post"
    ),
    Permission(
        name="blog.add_post",
        scope=PermissionScope.MODEL,
        action=PermissionAction.CREATE,
        resource="Post"
    ),
    Permission(
        name="blog.change_post",
        scope=PermissionScope.MODEL,
        action=PermissionAction.UPDATE,
        resource="Post"
    ),
    Permission(
        name="blog.delete_post",
        scope=PermissionScope.MODEL,
        action=PermissionAction.DELETE,
        resource="Post"
    ),

    # Category permissions
    Permission(
        name="blog.manage_categories",
        scope=PermissionScope.MODEL,
        action=PermissionAction.ADMIN,
        resource="Category"
    ),
]

def setup_blog_permissions(session, user):
    """Setup default blog permissions for a user"""

    # Author permissions - can manage their own posts
    author_perms = ModelPermission(
        model_name="Post",
        user_id=user.id,
        can_create=True,
        can_read=True,
        can_update=True,
        can_delete=False  # Only admins can delete
    )

    # Field-level permissions - authors can't edit meta fields
    meta_field_perm = FieldPermission(
        model_name="Post",
        field_name="meta_title",
        user_id=user.id,
        can_read=True,
        can_write=False  # Only SEO specialists can edit
    )

    session.add(author_perms)
    session.add(meta_field_perm)
    session.commit()

### 3. Add Permission Decorators to Views

Edit `apps/blog/views.py`:

```python
from fabiplus.core.permissions.decorators import (
    require_model_permission, require_field_permission,
    require_row_permission, owner_required
)

@router.post("/posts/")
@require_model_permission(PermissionAction.CREATE, model_class=Post)
async def create_post(
    post_data: PostCreate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Create a new blog post"""

    post = Post(
        **post_data.dict(),
        author_id=current_user.id
    )

    session.add(post)
    session.commit()
    session.refresh(post)

    return post

@router.get("/posts/{post_id}")
@require_row_permission(PermissionAction.READ, instance_param="post")
async def get_post(
    post_id: int,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Get a specific post"""

    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    return post

@router.put("/posts/{post_id}")
@owner_required(instance_param="post", owner_field="author_id")
async def update_post(
    post_id: int,
    post_data: PostUpdate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Update a post (only by owner)"""

    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    for field, value in post_data.dict(exclude_unset=True).items():
        setattr(post, field, value)

    session.commit()
    session.refresh(post)

    return post

@router.put("/posts/{post_id}/meta")
@require_field_permission(
    PermissionAction.UPDATE,
    field_name="meta_title",
    model_class=Post
)
async def update_post_meta(
    post_id: int,
    meta_data: PostMetaUpdate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Update post meta fields (SEO specialists only)"""

    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    post.meta_title = meta_data.meta_title
    post.meta_description = meta_data.meta_description

    session.commit()

    return {"message": "Meta data updated successfully"}
```

### 4. Test Permissions

```bash
# Test without permission (should fail)
curl -X POST "http://localhost:8000/api/v1/blog/posts/" \
  -H "Content-Type: application/json" \
  -d '{"title": "Test Post", "content": "Test content"}'

# Test with valid token
curl -X POST "http://localhost:8000/api/v1/blog/posts/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Test Post", "content": "Test content"}'

# Test owner-only update
curl -X PUT "http://localhost:8000/api/v1/blog/posts/1" \
  -H "Authorization: Bearer OWNER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Updated Title"}'
```

### 5. Advanced Permission Scenarios

```python
# Row-level permissions for draft posts
@router.get("/posts/drafts")
async def get_user_drafts(
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user),
    check_permission = Depends(check_permissions())
):
    """Get user's draft posts with row-level filtering"""

    # Check if user can view drafts
    can_view_drafts = await check_permission(
        "row:read:Post",
        conditions={"is_published": False, "author_id": current_user.id}
    )

    if not can_view_drafts:
        raise HTTPException(status_code=403, detail="Cannot view drafts")

    # Filter posts by ownership and draft status
    posts = session.exec(
        select(Post).where(
            Post.author_id == current_user.id,
            Post.is_published == False
        )
    ).all()

    return posts

---

## 📊 Response Types & Streaming

FABI+ provides powerful response types for handling large datasets and different formats:

### 1. Streaming JSON for Large Datasets

```python
from fabiplus.core.responses.streaming import StreamingJSONResponse

@router.get("/posts/export/json")
async def export_posts_json(
    session: Session = Depends(get_session)
):
    """Export all posts as streaming JSON"""

    async def post_generator():
        # Simulate large dataset
        offset = 0
        limit = 100

        while True:
            posts = session.exec(
                select(Post).offset(offset).limit(limit)
            ).all()

            if not posts:
                break

            for post in posts:
                yield {
                    "id": post.id,
                    "title": post.title,
                    "content": post.content,
                    "created_at": post.created_at.isoformat(),
                    "is_published": post.is_published
                }

            offset += limit

    return StreamingJSONResponse(
        data_generator=post_generator(),
        chunk_size=100,
        include_metadata=True
    )

### 2. CSV Export

```python
from fabiplus.core.responses.streaming import StreamingCSVResponse

@router.get("/posts/export/csv")
async def export_posts_csv():
    """Export posts as CSV"""

    def post_generator():
        posts = session.exec(select(Post)).all()

        for post in posts:
            yield {
                "ID": post.id,
                "Title": post.title,
                "Author": post.author.name if post.author else "Unknown",
                "Published": "Yes" if post.is_published else "No",
                "Created": post.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }

    return StreamingCSVResponse(
        data_generator=post_generator(),
        filename="blog_posts.csv",
        include_header=True
    )

### 3. Excel Export

```python
from fabiplus.core.responses.formats import ExcelResponse

@router.get("/posts/export/excel")
async def export_posts_excel(
    session: Session = Depends(get_session)
):
    """Export posts as Excel file"""

    posts = session.exec(select(Post)).all()

    # Prepare data for Excel
    posts_data = []
    categories_data = []

    for post in posts:
        posts_data.append({
            "ID": post.id,
            "Title": post.title,
            "Content": post.content[:100] + "..." if len(post.content) > 100 else post.content,
            "Published": post.is_published,
            "Created": post.created_at,
            "Category": post.category.name if post.category else "Uncategorized"
        })

    categories = session.exec(select(Category)).all()
    for category in categories:
        categories_data.append({
            "ID": category.id,
            "Name": category.name,
            "Post Count": len(category.posts)
        })

    # Multiple sheets
    excel_data = {
        "Posts": posts_data,
        "Categories": categories_data
    }

    return ExcelResponse(
        data=excel_data,
        filename="blog_export.xlsx",
        sheet_names=["Posts", "Categories"]
    )

### 4. PDF Report

```python
from fabiplus.core.responses.formats import PDFResponse

@router.get("/posts/{post_id}/pdf")
async def get_post_pdf(
    post_id: int,
    session: Session = Depends(get_session)
):
    """Generate PDF version of a post"""

    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Prepare content for PDF
    content = f"""
    {post.title}

    Published: {post.created_at.strftime("%B %d, %Y")}
    Author: {post.author.name if post.author else "Unknown"}
    Category: {post.category.name if post.category else "Uncategorized"}

    {post.content}
    """

    return PDFResponse(
        content=content,
        filename=f"{post.slug}.pdf"
    )

### 5. Server-Sent Events for Real-time Updates

```python
from fabiplus.core.responses.streaming import ServerSentEventsResponse

@router.get("/posts/live-updates")
async def post_live_updates():
    """Stream live post updates"""

    async def event_generator():
        # Simulate real-time updates
        import asyncio

        while True:
            # Check for new posts (in real app, use database triggers or Redis)
            yield {
                "event": "new_post",
                "data": {
                    "message": "New post published!",
                    "timestamp": datetime.now().isoformat()
                }
            }

            await asyncio.sleep(30)  # Check every 30 seconds

    return ServerSentEventsResponse(
        event_generator=event_generator()
    )

### 6. Paginated Responses

```python
from fabiplus.core.responses.pagination import PaginatedResponse, CursorPaginatedResponse

@router.get("/posts/paginated")
async def get_posts_paginated(
    page: int = 1,
    per_page: int = 10,
    session: Session = Depends(get_session)
):
    """Get paginated posts"""

    # Get total count
    total_count = session.exec(select(func.count(Post.id))).one()

    # Get posts for current page
    offset = (page - 1) * per_page
    posts = session.exec(
        select(Post).offset(offset).limit(per_page)
    ).all()

    return PaginatedResponse(
        data=[post.dict() for post in posts],
        page=page,
        per_page=per_page,
        total_items=total_count
    )

@router.get("/posts/cursor-paginated")
async def get_posts_cursor_paginated(
    cursor: Optional[str] = None,
    limit: int = 10,
    session: Session = Depends(get_session)
):
    """Get cursor-paginated posts (better for large datasets)"""

    query = select(Post).order_by(Post.id)

    if cursor:
        query = query.where(Post.id > int(cursor))

    posts = session.exec(query.limit(limit + 1)).all()

    has_next = len(posts) > limit
    if has_next:
        posts = posts[:-1]

    next_cursor = str(posts[-1].id) if posts and has_next else None

    return CursorPaginatedResponse(
        data=[post.dict() for post in posts],
        next_cursor=next_cursor,
        has_next=has_next,
        cursor_field="id"
    )

---

## 🎛️ Admin Interface

FABI+ includes a powerful Django-style admin interface:

### 1. Access Admin Interface

Visit: http://localhost:8000/admin

### 2. Configure Admin

Edit `apps/blog/admin.py`:

```python
from fabiplus.core.admin import admin_site, ModelAdmin

@admin_site.register(Post)
class PostAdmin(ModelAdmin):
    list_display = ['title', 'category', 'is_published', 'created_at']
    list_filter = ['is_published', 'category', 'created_at']
    search_fields = ['title', 'content']

    fieldsets = [
        ('Basic Information', {
            'fields': ['title', 'slug', 'category']
        }),
        ('Content', {
            'fields': ['content', 'excerpt']
        }),
        ('Publishing', {
            'fields': ['is_published', 'is_featured']
        }),
    ]

@admin_site.register(Category)
class CategoryAdmin(ModelAdmin):
    list_display = ['name', 'slug', 'is_active']
    prepopulated_fields = {'slug': ('name',)}
```

### 3. Admin Features

- ✅ Model CRUD operations
- ✅ Advanced filtering and search
- ✅ Bulk actions
- ✅ Relationship management
- ✅ File uploads
- ✅ Permission integration
- ✅ Custom views

---

## 🧪 Testing Everything

### 1. API Testing

```bash
# Test basic endpoints
curl http://localhost:8000/api/v1/blog/posts/
curl http://localhost:8000/api/v1/blog/categories/

# Test with authentication
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/blog/posts/

# Test file upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-image" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "file=@test-image.jpg"

# Test CSV export
curl "http://localhost:8000/api/v1/blog/posts/export/csv" \
     -o posts.csv

# Test Excel export
curl "http://localhost:8000/api/v1/blog/posts/export/excel" \
     -o posts.xlsx

# Test PDF generation
curl "http://localhost:8000/api/v1/blog/posts/1/pdf" \
     -o post.pdf
```

### 2. Permission Testing

```bash
# Test without permission (should fail)
curl -X POST "http://localhost:8000/api/v1/blog/posts/" \
     -H "Content-Type: application/json" \
     -d '{"title": "Test Post", "content": "Test content"}'

# Test with valid token
curl -X POST "http://localhost:8000/api/v1/blog/posts/" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title": "Test Post", "content": "Test content"}'

# Test owner-only update
curl -X PUT "http://localhost:8000/api/v1/blog/posts/1" \
     -H "Authorization: Bearer OWNER_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title": "Updated Title"}'
```

### 3. Media System Testing

```bash
# Test single image upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-image" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "file=@test-image.jpg" \
     -F "alt_text=Test blog image"

# Test multiple upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-multiple" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "files=@image1.jpg" \
     -F "files=@image2.jpg" \
     -F "files=@image3.jpg"

# Test chunked upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-large/start" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "filename=large-video.mp4" \
     -F "total_size=104857600" \
     -F "chunk_size=1048576"
```

### 4. Streaming Responses Testing

```bash
# Test streaming JSON
curl "http://localhost:8000/api/v1/blog/posts/export/json" \
     -o large-dataset.json

# Test Server-Sent Events
curl "http://localhost:8000/api/v1/blog/posts/live-updates"

# Test paginated responses
curl "http://localhost:8000/api/v1/blog/posts/paginated?page=1&per_page=10"

# Test cursor pagination
curl "http://localhost:8000/api/v1/blog/posts/cursor-paginated?limit=10"
```

---

## 🌍 Real-World Examples

### 1. Complete Blog Platform

```python
# Full blog implementation with all FABI+ features
# File: examples/blog_platform/

# Models with relationships
class Author(BaseModel, table=True):
    name: str
    email: str
    bio: Optional[str]
    avatar_id: Optional[int] = Field(foreign_key="media_files.id")

class Post(BaseModel, table=True):
    title: str
    slug: str
    content: str
    author_id: int = Field(foreign_key="authors.id")
    category_id: Optional[int] = Field(foreign_key="categories.id")
    featured_image_id: Optional[int] = Field(foreign_key="media_files.id")

    # SEO
    meta_title: Optional[str]
    meta_description: Optional[str]

    # Publishing
    is_published: bool = Field(default=False)
    published_at: Optional[datetime]

# Advanced views with all features
@router.get("/posts/", response_model=PaginatedResponse)
@require_permission("blog.view_post")
async def list_posts(
    page: int = 1,
    per_page: int = 10,
    category: Optional[str] = None,
    search: Optional[str] = None,
    format: Optional[str] = None,  # json, csv, excel, pdf
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """List posts with filtering, search, and multiple export formats"""

    query = select(Post)

    # Apply filters
    if category:
        query = query.join(Category).where(Category.slug == category)

    if search:
        query = query.where(Post.title.contains(search))

    # Handle different response formats
    if format == "csv":
        return StreamingCSVResponse(
            data_generator=post_generator(query),
            filename="posts.csv"
        )
    elif format == "excel":
        posts = session.exec(query).all()
        return ExcelResponse(
            data={"Posts": [post.dict() for post in posts]},
            filename="posts.xlsx"
        )
    elif format == "json-stream":
        return StreamingJSONResponse(
            data_generator=post_generator(query),
            chunk_size=100
        )

    # Default paginated response
    total_count = session.exec(select(func.count(Post.id))).one()
    offset = (page - 1) * per_page
    posts = session.exec(query.offset(offset).limit(per_page)).all()

    return PaginatedResponse(
        data=[post.dict() for post in posts],
        page=page,
        per_page=per_page,
        total_items=total_count
    )
```

### 2. E-commerce API

```python
# E-commerce example with inventory, orders, and payments
# File: examples/ecommerce_api/

class Product(BaseModel, table=True):
    name: str
    slug: str
    description: str
    price: Decimal
    stock_quantity: int
    category_id: int = Field(foreign_key="categories.id")

    # Media
    images: List[str] = Field(default=[], description="Image IDs")

    # SEO
    meta_title: Optional[str]
    meta_description: Optional[str]

class Order(BaseModel, table=True):
    order_number: str = Field(unique=True)
    customer_id: int = Field(foreign_key="users.id")
    status: OrderStatus = Field(default=OrderStatus.PENDING)
    total_amount: Decimal

    # Shipping
    shipping_address: Dict[str, Any]
    shipping_method: str

    # Payment
    payment_status: PaymentStatus = Field(default=PaymentStatus.PENDING)
    payment_method: Optional[str]

# Advanced inventory management
@router.put("/products/{product_id}/inventory")
@require_model_permission(PermissionAction.UPDATE, model_class=Product)
async def update_inventory(
    product_id: int,
    quantity_change: int,
    reason: str,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Update product inventory with audit trail"""

    product = session.get(Product, product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Check permissions for inventory management
    if not await check_permission(
        current_user,
        "inventory:update:Product",
        resource=product
    ):
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Update inventory
    old_quantity = product.stock_quantity
    product.stock_quantity += quantity_change

    # Create audit log
    audit_log = InventoryAuditLog(
        product_id=product.id,
        old_quantity=old_quantity,
        new_quantity=product.stock_quantity,
        change_reason=reason,
        changed_by=current_user.id
    )

    session.add(audit_log)
    session.commit()

    return {
        "product_id": product.id,
        "old_quantity": old_quantity,
        "new_quantity": product.stock_quantity,
        "change": quantity_change
    }
```

---

## 🚀 Production Deployment

### 1. Environment Setup

```bash
# Production environment variables
export SECRET_KEY="your-secret-key"
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
export REDIS_URL="redis://localhost:6379"
export MEDIA_ROOT="/var/www/media"
```

### 2. Docker Deployment

```dockerfile
# Dockerfile (generated with --docker flag)
FROM python:3.11-slim

WORKDIR /app

COPY pyproject.toml poetry.lock ./
RUN pip install poetry && poetry install --no-dev

COPY . .

EXPOSE 8000

CMD ["fabiplus", "server", "run", "--production", "--host", "0.0.0.0"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/myblog
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - media_files:/var/www/media

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: myblog
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine

volumes:
  postgres_data:
  media_files:
```

### 3. Production Commands

```bash
# Run production server
fabiplus server run --production --host 0.0.0.0 --port 8000

# With Docker
docker-compose up -d

# Scale with multiple workers
docker-compose up -d --scale web=4
```

---

## 🎯 Summary

You now have a complete understanding of FABI+ framework! Here's what you've learned:

### ✅ **Core Features Implemented:**
1. **Multi-ORM Support** - SQLModel, SQLAlchemy, Tortoise
2. **Advanced Media System** - File uploads, processing, streaming
3. **Granular Permissions** - Model, field, and row-level access control
4. **Response Types & Streaming** - Handle large datasets efficiently

### ✅ **Production-Ready Features:**
- Django-style admin interface
- Comprehensive CLI tooling
- Advanced caching strategies
- Security middleware
- Performance optimization
- Complete testing framework

### ✅ **Developer Experience:**
- Type safety throughout
- Rich error handling
- Extensive documentation
- Real-world examples
- Migration guides

### 🚀 **Next Steps:**
1. Build your own project using this guide
2. Explore the admin interface
3. Test all the features
4. Deploy to production
5. Contribute to the framework

**FABI+ is now ready for production use!** 🎉

---

## 📚 Additional Resources

- **GitHub Repository:** https://github.com/Helevon-Technologies-LTD/fabiplus
- **API Documentation:** http://localhost:8000/docs (when running)
- **Admin Interface:** http://localhost:8000/admin (when running)
- **Examples:** Check the `examples/` directory in the repository

---

*Happy coding with FABI+! 🚀*
```
```
```
```
```
```