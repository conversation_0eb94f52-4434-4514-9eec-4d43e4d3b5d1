# FABI+ Permission System Documentation

## Overview

FABI+ provides a comprehensive, multi-layered permission system that supports:

- **OAuth2 Authentication** with JWT tokens
- **Model-level permissions** (CRUD operations on entire models)
- **Field-level permissions** (access control for specific fields)
- **Row-level permissions** (access control for specific records)
- **Role-based permissions** (permissions assigned through roles)
- **User and Group permissions** (direct permission assignments)

## Authentication System

### OAuth2 with JWT Tokens

FABI+ uses OAuth2 with JWT tokens for authentication:

```python
# Get authentication token
POST /auth/token
Content-Type: application/x-www-form-urlencoded

username=your_username&password=your_password
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### Using Authentication Tokens

Include the token in the Authorization header:

```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### Current User Information

Get current authenticated user information:

```http
GET /auth/me
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "user-uuid",
  "username": "admin",
  "email": "<EMAIL>",
  "is_staff": true,
  "is_superuser": true,
  "first_name": "Admin",
  "last_name": "User"
}
```

## Permission Levels

### 1. Model-Level Permissions

Control access to entire models (tables). Permissions include:

- `can_create`: Create new records
- `can_read`: Read/view records
- `can_update`: Modify existing records
- `can_delete`: Delete records
- `can_list`: List/browse records

**Model Permission Structure:**
```json
{
  "model_name": "user",
  "user_id": "user-uuid",
  "role_id": null,
  "can_create": true,
  "can_read": true,
  "can_update": false,
  "can_delete": false,
  "can_list": true,
  "conditions": "{}",
  "is_active": true
}
```

### 2. Field-Level Permissions

Control access to specific fields within a model:

- `can_read`: View field value
- `can_write`: Modify field value
- `can_admin`: Administrative access to field
- `is_sensitive`: Mark field as sensitive data
- `mask_value`: Mask field value in responses

**Field Permission Structure:**
```json
{
  "model_name": "user",
  "field_name": "email",
  "user_id": "user-uuid",
  "role_id": null,
  "can_read": true,
  "can_write": false,
  "can_admin": false,
  "is_sensitive": true,
  "mask_value": true,
  "conditions": "{}"
}
```

### 3. Row-Level Permissions

Control access to specific records (rows):

- `can_read`: View specific record
- `can_update`: Modify specific record
- `can_delete`: Delete specific record
- `can_admin`: Administrative access to record
- `is_owner`: Mark user as owner of record

**Row Permission Structure:**
```json
{
  "model_name": "user",
  "row_id": "record-uuid",
  "user_id": "user-uuid",
  "role_id": null,
  "can_read": true,
  "can_update": false,
  "can_delete": false,
  "can_admin": false,
  "is_owner": false,
  "owner_field": "created_by",
  "conditions": "{}"
}
```

## Permission Types

### User Permissions
Direct permissions assigned to individual users.

### Group Permissions
Permissions assigned to groups, inherited by group members.

### Role Permissions
Permissions assigned to roles, inherited by users with those roles.

### Permission Templates
Reusable permission configurations that can be applied to multiple users/roles.

## Creating Views with Authentication

### Basic Authenticated View

```python
from fabiplus.core.views import AuthenticatedGenericAPIView
from fabiplus.core.models import User

class UserView(AuthenticatedGenericAPIView):
    model = User
    
    # This view automatically requires authentication
    # and checks permissions for all CRUD operations
```

### Custom Permission Checks

```python
from fabiplus.core.views import AuthenticatedGenericAPIView
from fabiplus.core.permissions.enums import PermissionAction
from fabiplus.core.models import User

class CustomUserView(AuthenticatedGenericAPIView):
    model = User
    
    def list(self, session, *args, current_user=None, **kwargs):
        # Custom permission logic
        if not current_user.is_staff:
            raise HTTPException(status_code=403, detail="Staff access required")
        
        return super().list(session, *args, current_user=current_user, **kwargs)
    
    def create(self, data, session, current_user=None):
        # Custom creation logic with permission check
        if not current_user.is_superuser:
            # Remove sensitive fields for non-superusers
            data.pop('is_superuser', None)
            data.pop('is_staff', None)
        
        return super().create(data, session, current_user=current_user)
```

## Model Registration with Authentication

### Authenticated Model Registration

```python
from fabiplus.core.registry import ModelRegistry
from fabiplus.core.views import AuthenticatedGenericAPIView
from your_app.models import YourModel

# Register model with authentication required
ModelRegistry.register_model(
    YourModel,
    view_class=AuthenticatedGenericAPIView,
    tags=["Your Model"]
)
```

### Public Model Registration

```python
from fabiplus.core.registry import ModelRegistry
from fabiplus.core.views import GenericAPIView
from your_app.models import PublicModel

# Register model without authentication
ModelRegistry.register_model(
    PublicModel,
    view_class=GenericAPIView,
    tags=["Public"]
)
```

## Permission System Workflow

### 1. Authentication Flow

1. User sends credentials to `/auth/token`
2. Server validates credentials
3. Server returns JWT token
4. Client includes token in subsequent requests
5. Server validates token and extracts user information

### 2. Permission Check Flow

1. Request arrives at authenticated endpoint
2. JWT token is validated and user extracted
3. Permission service checks user permissions:
   - Check if user is superuser (bypass all checks)
   - Check model-level permissions
   - Check field-level permissions (if applicable)
   - Check row-level permissions (if applicable)
4. If permissions granted, request proceeds
5. If permissions denied, return 403 Forbidden

### 3. Permission Hierarchy

1. **Superuser**: Bypasses all permission checks
2. **Staff**: May have elevated permissions based on configuration
3. **Model Permissions**: Control access to entire models
4. **Field Permissions**: Control access to specific fields
5. **Row Permissions**: Control access to specific records

## API Endpoints

### Permission Management Endpoints

All permission endpoints require authentication and appropriate permissions:

- `GET /api/v1/userpermission` - List user permissions
- `POST /api/v1/userpermission` - Create user permission
- `GET /api/v1/grouppermission` - List group permissions
- `POST /api/v1/grouppermission` - Create group permission
- `GET /api/v1/rolepermission` - List role permissions
- `POST /api/v1/rolepermission` - Create role permission
- `GET /api/v1/modelpermission` - List model permissions
- `POST /api/v1/modelpermission` - Create model permission
- `GET /api/v1/fieldpermission` - List field permissions
- `POST /api/v1/fieldpermission` - Create field permission
- `GET /api/v1/rowpermission` - List row permissions
- `POST /api/v1/rowpermission` - Create row permission
- `GET /api/v1/permissiontemplate` - List permission templates
- `POST /api/v1/permissiontemplate` - Create permission template

## Testing the Permission System

### Test Authentication

```python
import requests

# Test authentication
response = requests.post("http://localhost:8000/auth/token", data={
    "username": "admin",
    "password": "admin123"
})

if response.status_code == 200:
    token = response.json()["access_token"]
    print(f"✅ Authentication successful")
else:
    print(f"❌ Authentication failed: {response.status_code}")
```

### Test Protected Endpoints

```python
headers = {"Authorization": f"Bearer {token}"}

# Test protected endpoint
response = requests.get("http://localhost:8000/api/v1/user", headers=headers)

if response.status_code == 200:
    print("✅ Access granted")
elif response.status_code == 403:
    print("⚠️ Permission denied")
elif response.status_code == 401:
    print("❌ Authentication required")
```

## Best Practices

### 1. Security

- Always use HTTPS in production
- Set appropriate JWT token expiration times
- Regularly rotate JWT secrets
- Implement proper password policies
- Use secure password hashing (bcrypt/scrypt)

### 2. Permission Design

- Follow principle of least privilege
- Use role-based permissions for common access patterns
- Use field-level permissions for sensitive data
- Use row-level permissions for data isolation
- Document permission requirements clearly

### 3. Performance

- Cache permission checks when possible
- Use database indexes on permission tables
- Minimize permission check complexity
- Consider permission inheritance patterns

### 4. Maintenance

- Regularly audit permissions
- Remove unused permissions
- Monitor permission usage
- Keep permission documentation updated

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Token missing or invalid
2. **403 Forbidden**: Valid token but insufficient permissions
3. **500 Internal Server Error**: Permission system configuration error

### Debug Tips

- Check server logs for permission check details
- Verify user roles and permissions in database
- Test with superuser account to isolate permission issues
- Use permission test scripts to validate configuration
