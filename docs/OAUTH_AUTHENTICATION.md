# FABI+ OAuth Authentication Guide

## Overview

FABI+ implements OAuth2 authentication with <PERSON><PERSON><PERSON> (JSON Web Tokens) for secure API access. This guide covers setup, configuration, and usage of the authentication system.

## Authentication Flow

### 1. Token-Based Authentication

FABI+ uses the OAuth2 "Resource Owner Password Credentials" flow with JWT tokens:

```
Client -> POST /auth/token (username, password) -> Server
Server -> Validates credentials -> Returns JWT token
Client -> Uses token in Authorization header -> Protected endpoints
```

### 2. JWT Token Structure

JWT tokens contain:
- **Header**: Token type and signing algorithm
- **Payload**: User information and claims
- **Signature**: Cryptographic signature for verification

## Setup and Configuration

### 1. Environment Variables

Configure authentication settings in your environment:

```bash
# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Security Settings
SECURITY_ENABLED=true
CORS_ENABLED=true
```

### 2. User Model Requirements

Your User model must include these fields for authentication:

```python
from sqlmodel import SQLModel, Field
from typing import Optional
import uuid

class User(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    username: str = Field(unique=True, index=True)
    email: str = Field(unique=True, index=True)
    hashed_password: str
    is_active: bool = Field(default=True)
    is_staff: bool = Field(default=False)
    is_superuser: bool = Field(default=False)
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
```

## Authentication Endpoints

### 1. Token Endpoint

**Endpoint:** `POST /auth/token`

**Request:**
```http
POST /auth/token HTTP/1.1
Content-Type: application/x-www-form-urlencoded

username=your_username&password=your_password
```

**Response (Success):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

**Response (Error):**
```json
{
  "detail": "Incorrect username or password"
}
```

### 2. Current User Endpoint

**Endpoint:** `GET /auth/me`

**Request:**
```http
GET /auth/me HTTP/1.1
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "id": "6dc31ae5-8823-4ae8-b5fa-3aa1d1104a76",
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "Admin",
  "last_name": "User",
  "full_name": "Admin User",
  "is_active": true,
  "is_staff": true,
  "is_superuser": true,
  "last_login": "2025-01-15T16:30:00Z",
  "created_at": "2025-01-01T10:00:00Z"
}
```

## Using Authentication in Code

### 1. Python/Requests Example

```python
import requests
import json

BASE_URL = "http://localhost:8000"

class APIClient:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.token = None
    
    def authenticate(self, username, password):
        """Get authentication token"""
        response = requests.post(
            f"{self.base_url}/auth/token",
            data={"username": username, "password": password}
        )
        
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            return True
        return False
    
    def get_headers(self):
        """Get headers with authentication"""
        if not self.token:
            raise Exception("Not authenticated")
        return {"Authorization": f"Bearer {self.token}"}
    
    def get_current_user(self):
        """Get current user information"""
        response = requests.get(
            f"{self.base_url}/auth/me",
            headers=self.get_headers()
        )
        return response.json() if response.status_code == 200 else None
    
    def api_request(self, method, endpoint, **kwargs):
        """Make authenticated API request"""
        url = f"{self.base_url}/api/v1{endpoint}"
        headers = self.get_headers()
        
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
        kwargs['headers'] = headers
        
        return requests.request(method, url, **kwargs)

# Usage example
client = APIClient()

# Authenticate
if client.authenticate("admin", "admin123"):
    print("✅ Authentication successful")
    
    # Get current user
    user = client.get_current_user()
    print(f"Logged in as: {user['username']}")
    
    # Make authenticated requests
    response = client.api_request("GET", "/user")
    if response.status_code == 200:
        users = response.json()
        print(f"Found {users.get('total', 0)} users")
else:
    print("❌ Authentication failed")
```

### 2. JavaScript/Fetch Example

```javascript
class APIClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.token = null;
    }
    
    async authenticate(username, password) {
        const response = await fetch(`${this.baseUrl}/auth/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
        });
        
        if (response.ok) {
            const data = await response.json();
            this.token = data.access_token;
            return true;
        }
        return false;
    }
    
    getHeaders() {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getCurrentUser() {
        const response = await fetch(`${this.baseUrl}/auth/me`, {
            headers: this.getHeaders()
        });
        return response.ok ? await response.json() : null;
    }
    
    async apiRequest(method, endpoint, data = null) {
        const url = `${this.baseUrl}/api/v1${endpoint}`;
        const options = {
            method,
            headers: this.getHeaders()
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        return await fetch(url, options);
    }
}

// Usage example
const client = new APIClient();

async function example() {
    // Authenticate
    if (await client.authenticate('admin', 'admin123')) {
        console.log('✅ Authentication successful');
        
        // Get current user
        const user = await client.getCurrentUser();
        console.log(`Logged in as: ${user.username}`);
        
        // Make authenticated requests
        const response = await client.apiRequest('GET', '/user');
        if (response.ok) {
            const users = await response.json();
            console.log(`Found ${users.total || 0} users`);
        }
    } else {
        console.log('❌ Authentication failed');
    }
}
```

## Creating Authenticated Views

### 1. Basic Authenticated View

```python
from fabiplus.core.views import AuthenticatedGenericAPIView
from your_app.models import YourModel

class YourModelView(AuthenticatedGenericAPIView):
    model = YourModel
    
    # All CRUD operations automatically require authentication
    # and check permissions
```

### 2. Custom Authentication Logic

```python
from fabiplus.core.views import AuthenticatedGenericAPIView
from fastapi import HTTPException, status

class CustomView(AuthenticatedGenericAPIView):
    model = YourModel
    
    def list(self, session, *args, current_user=None, **kwargs):
        # Custom authentication check
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is inactive"
            )
        
        # Additional business logic
        if current_user.is_staff:
            # Staff can see all records
            return super().list(session, *args, current_user=current_user, **kwargs)
        else:
            # Regular users see only their own records
            kwargs['filters'] = {'created_by': current_user.id}
            return super().list(session, *args, current_user=current_user, **kwargs)
```

### 3. Mixed Authentication (Some endpoints public, some protected)

```python
from fabiplus.core.views import GenericAPIView
from fabiplus.core.auth import get_current_active_user
from fastapi import Depends, HTTPException

class MixedView(GenericAPIView):
    model = YourModel
    
    def list(self, session, *args, **kwargs):
        # Public endpoint - no authentication required
        return super().list(session, *args, **kwargs)
    
    def create(self, data, session, current_user = Depends(get_current_active_user)):
        # Protected endpoint - authentication required
        if not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Add user information to data
        data['created_by'] = current_user.id
        return super().create(data, session)
```

## User Management

### 1. Creating Users

Use the CLI command to create users:

```bash
# Create regular user
fabiplus user create --username john --email <EMAIL> --password secret123

# Create staff user
fabiplus user create --username staff --email <EMAIL> --password secret123 --staff

# Create superuser
fabiplus user create --username admin --email <EMAIL> --password admin123 --superuser
```

### 2. User Roles

- **Regular User**: Basic access, subject to all permission checks
- **Staff User**: May have elevated permissions based on configuration
- **Superuser**: Bypasses all permission checks, full system access

## Security Best Practices

### 1. Token Security

- Use strong, random JWT secret keys
- Set appropriate token expiration times
- Rotate JWT secrets regularly
- Store tokens securely on client side

### 2. Password Security

- Enforce strong password policies
- Use secure password hashing (bcrypt)
- Implement account lockout after failed attempts
- Require password changes periodically

### 3. HTTPS and Transport Security

- Always use HTTPS in production
- Implement proper CORS policies
- Use secure headers (HSTS, CSP, etc.)
- Validate all input data

### 4. Monitoring and Logging

- Log authentication attempts
- Monitor for suspicious activity
- Implement rate limiting
- Track token usage patterns

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Token missing from request
   - Token expired
   - Invalid token format
   - JWT secret mismatch

2. **403 Forbidden**
   - Valid token but insufficient permissions
   - User account inactive
   - User lacks required role

3. **Token Expiration**
   - Implement token refresh mechanism
   - Handle expiration gracefully in client
   - Provide clear error messages

### Debug Tips

- Check server logs for authentication details
- Verify JWT token structure and claims
- Test with different user roles
- Use authentication test scripts
- Validate environment configuration

## Testing Authentication

### Test Script Example

```python
#!/usr/bin/env python3
"""Test authentication system"""

import requests

def test_authentication():
    base_url = "http://localhost:8000"
    
    # Test valid credentials
    response = requests.post(f"{base_url}/auth/token", data={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ Authentication successful")
        
        # Test protected endpoint
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{base_url}/auth/me", headers=headers)
        
        if response.status_code == 200:
            user = response.json()
            print(f"✅ Current user: {user['username']}")
        else:
            print("❌ Failed to get current user")
    else:
        print("❌ Authentication failed")

if __name__ == "__main__":
    test_authentication()
```

This comprehensive authentication system provides secure, scalable access control for your FABI+ applications.
