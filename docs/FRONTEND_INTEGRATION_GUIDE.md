# FABI+ Blog System - Frontend Integration Guide

## Overview

This guide explains how to integrate with the FABI+ Blog System API from frontend applications. The system provides a complete blog platform with authentication, role-based permissions, and CRUD operations.

## 🚀 Quick Start

### Base Configuration

```javascript
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:8000',
  apiBase: 'http://127.0.0.1:8000/api/v1',
  timeout: 10000
};
```

### Authentication Setup

```javascript
class BlogAPI {
  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.apiBase = API_CONFIG.apiBase;
    this.token = localStorage.getItem('auth_token');
  }

  // Get authentication headers
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    return headers;
  }

  // Handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Unknown error' }));
      throw new Error(error.detail || `HTTP ${response.status}`);
    }
    return await response.json();
  }
}
```

## 🔐 Authentication

### User Registration

```javascript
async function registerUser(userData) {
  const response = await fetch(`${API_CONFIG.baseURL}/auth/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: userData.username,
      email: userData.email,
      password: userData.password,
      first_name: userData.firstName,
      last_name: userData.lastName
    })
  });

  if (response.ok) {
    const user = await response.json();
    console.log('User registered:', user);
    return user;
  } else {
    const error = await response.json();
    throw new Error(error.detail);
  }
}

// Usage
try {
  const newUser = await registerUser({
    username: 'johndoe',
    email: '<EMAIL>',
    password: 'securepass123',
    firstName: 'John',
    lastName: 'Doe'
  });
  console.log('Registration successful:', newUser);
} catch (error) {
  console.error('Registration failed:', error.message);
}
```

### User Login

```javascript
async function loginUser(username, password) {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);

  const response = await fetch(`${API_CONFIG.baseURL}/auth/token`, {
    method: 'POST',
    body: formData
  });

  if (response.ok) {
    const data = await response.json();
    const token = data.access_token;
    
    // Store token
    localStorage.setItem('auth_token', token);
    
    return token;
  } else {
    throw new Error('Login failed');
  }
}

// Usage
try {
  const token = await loginUser('admin', 'admin123');
  console.log('Login successful');
} catch (error) {
  console.error('Login failed:', error.message);
}
```

### Get Current User

```javascript
async function getCurrentUser() {
  const token = localStorage.getItem('auth_token');
  if (!token) throw new Error('No authentication token');

  const response = await fetch(`${API_CONFIG.baseURL}/auth/me`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });

  if (response.ok) {
    return await response.json();
  } else {
    throw new Error('Failed to get user info');
  }
}
```

## 📝 Blog Operations

### Blog Categories

```javascript
class CategoryAPI extends BlogAPI {
  // Get all categories (public)
  async getCategories() {
    const response = await fetch(`${this.apiBase}/blogcategory`);
    return this.handleResponse(response);
  }

  // Create category (requires staff permission)
  async createCategory(categoryData) {
    const response = await fetch(`${this.apiBase}/blogcategory`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        name: categoryData.name,
        description: categoryData.description,
        slug: categoryData.slug,
        is_active: true
      })
    });
    return this.handleResponse(response);
  }

  // Update category (requires staff permission)
  async updateCategory(id, categoryData) {
    const response = await fetch(`${this.apiBase}/blogcategory/${id}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(categoryData)
    });
    return this.handleResponse(response);
  }

  // Delete category (requires staff permission)
  async deleteCategory(id) {
    const response = await fetch(`${this.apiBase}/blogcategory/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders()
    });
    return this.handleResponse(response);
  }
}
```

### Blog Posts

```javascript
class PostAPI extends BlogAPI {
  // Get published posts (public)
  async getPosts(page = 1, limit = 10) {
    const response = await fetch(`${this.apiBase}/blogpost?page=${page}&limit=${limit}`);
    return this.handleResponse(response);
  }

  // Get single post (public)
  async getPost(id) {
    const response = await fetch(`${this.apiBase}/blogpost/${id}`);
    return this.handleResponse(response);
  }

  // Create post (requires authentication)
  async createPost(postData) {
    const response = await fetch(`${this.apiBase}/blogpost`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        title: postData.title,
        content: postData.content,
        excerpt: postData.excerpt,
        slug: postData.slug,
        category_id: postData.categoryId,
        is_published: postData.isPublished || false,
        is_featured: postData.isFeatured || false
      })
    });
    return this.handleResponse(response);
  }

  // Update post (requires ownership or staff permission)
  async updatePost(id, postData) {
    const response = await fetch(`${this.apiBase}/blogpost/${id}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(postData)
    });
    return this.handleResponse(response);
  }

  // Delete post (requires ownership or staff permission)
  async deletePost(id) {
    const response = await fetch(`${this.apiBase}/blogpost/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders()
    });
    return this.handleResponse(response);
  }

  // Get featured posts
  async getFeaturedPosts() {
    const response = await fetch(`${this.baseURL}/blog/featured/`);
    return this.handleResponse(response);
  }

  // Get recent posts
  async getRecentPosts() {
    const response = await fetch(`${this.baseURL}/blog/recent/`);
    return this.handleResponse(response);
  }
}
```

### Blog Tags

```javascript
class TagAPI extends BlogAPI {
  // Get all tags (public)
  async getTags() {
    const response = await fetch(`${this.apiBase}/blogtag`);
    return this.handleResponse(response);
  }

  // Create tag (requires staff permission)
  async createTag(tagData) {
    const response = await fetch(`${this.apiBase}/blogtag`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        name: tagData.name,
        slug: tagData.slug,
        description: tagData.description,
        color: tagData.color || '#007bff',
        is_active: true
      })
    });
    return this.handleResponse(response);
  }
}
```

### Comments

```javascript
class CommentAPI extends BlogAPI {
  // Get approved comments for a post (public)
  async getComments(postId) {
    const response = await fetch(`${this.apiBase}/blogcomment?post_id=${postId}`);
    return this.handleResponse(response);
  }

  // Create comment (authenticated or guest)
  async createComment(commentData) {
    const response = await fetch(`${this.apiBase}/blogcomment`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        content: commentData.content,
        post_id: commentData.postId,
        parent_id: commentData.parentId || null,
        // For guest comments (if not authenticated)
        guest_name: commentData.guestName,
        guest_email: commentData.guestEmail,
        guest_website: commentData.guestWebsite
      })
    });
    return this.handleResponse(response);
  }
}
```

## 🎨 React Integration Examples

### Authentication Hook

```jsx
import { useState, useEffect, createContext, useContext } from 'react';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('auth_token'));
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (token) {
      getCurrentUser()
        .then(setUser)
        .catch(() => {
          localStorage.removeItem('auth_token');
          setToken(null);
        })
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  }, [token]);

  const login = async (username, password) => {
    const newToken = await loginUser(username, password);
    setToken(newToken);
    const userData = await getCurrentUser();
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setToken(null);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
```

### Blog Post Component

```jsx
import { useState, useEffect } from 'react';
import { useAuth } from './AuthProvider';

function BlogPost({ postId }) {
  const [post, setPost] = useState(null);
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const postAPI = new PostAPI();
  const commentAPI = new CommentAPI();

  useEffect(() => {
    Promise.all([
      postAPI.getPost(postId),
      commentAPI.getComments(postId)
    ])
    .then(([postData, commentsData]) => {
      setPost(postData);
      setComments(commentsData.items || []);
    })
    .catch(console.error)
    .finally(() => setLoading(false));
  }, [postId]);

  const handleComment = async (commentData) => {
    try {
      const newComment = await commentAPI.createComment({
        ...commentData,
        postId
      });
      
      if (user) {
        // Authenticated comment - appears immediately
        setComments(prev => [...prev, newComment]);
      } else {
        // Guest comment - show pending message
        alert('Comment submitted for moderation');
      }
    } catch (error) {
      console.error('Failed to post comment:', error);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!post) return <div>Post not found</div>;

  return (
    <article>
      <h1>{post.title}</h1>
      <div className="post-meta">
        <span>Views: {post.view_count}</span>
        {post.published_at && (
          <span>Published: {new Date(post.published_at).toLocaleDateString()}</span>
        )}
      </div>
      <div dangerouslySetInnerHTML={{ __html: post.content }} />
      
      <section className="comments">
        <h3>Comments ({comments.length})</h3>
        {comments.map(comment => (
          <div key={comment.id} className="comment">
            <strong>{comment.author_name || comment.guest_name}</strong>
            <p>{comment.content}</p>
          </div>
        ))}
        
        <CommentForm onSubmit={handleComment} />
      </section>
    </article>
  );
}
```

### Admin Dashboard Component

```jsx
function AdminDashboard() {
  const [stats, setStats] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user?.is_staff) {
      fetch(`${API_CONFIG.baseURL}/blog/stats/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('auth_token')}` }
      })
      .then(response => response.json())
      .then(setStats)
      .catch(console.error);
    }
  }, [user]);

  if (!user?.is_staff) {
    return <div>Access denied. Staff access required.</div>;
  }

  return (
    <div className="admin-dashboard">
      <h1>Blog Dashboard</h1>
      {stats && (
        <div className="stats-grid">
          <div className="stat-card">
            <h3>Total Posts</h3>
            <p>{stats.total_posts}</p>
          </div>
          <div className="stat-card">
            <h3>Published Posts</h3>
            <p>{stats.published_posts}</p>
          </div>
          <div className="stat-card">
            <h3>Comments</h3>
            <p>{stats.total_comments}</p>
          </div>
          <div className="stat-card">
            <h3>Categories</h3>
            <p>{stats.total_categories}</p>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🔒 Permission Handling

### Role-Based UI

```jsx
function PostActions({ post }) {
  const { user } = useAuth();
  
  const canEdit = user && (
    user.is_staff || 
    user.id === post.author_id
  );
  
  const canDelete = user && (
    user.is_staff || 
    user.id === post.author_id
  );

  return (
    <div className="post-actions">
      {canEdit && (
        <button onClick={() => editPost(post.id)}>
          Edit
        </button>
      )}
      {canDelete && (
        <button onClick={() => deletePost(post.id)}>
          Delete
        </button>
      )}
    </div>
  );
}
```

## 🚀 Production Deployment

### Environment Configuration

```javascript
const config = {
  development: {
    baseURL: 'http://127.0.0.1:8000',
    apiBase: 'http://127.0.0.1:8000/api/v1'
  },
  production: {
    baseURL: 'https://your-domain.com',
    apiBase: 'https://your-domain.com/api/v1'
  }
};

export const API_CONFIG = config[process.env.NODE_ENV] || config.development;
```

### Error Handling

```javascript
class APIError extends Error {
  constructor(message, status, details) {
    super(message);
    this.status = status;
    this.details = details;
  }
}

async function apiRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new APIError(
        error.detail || `HTTP ${response.status}`,
        response.status,
        error
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof APIError) throw error;
    throw new APIError('Network error', 0, { originalError: error });
  }
}
```

This guide provides everything needed to integrate with the FABI+ Blog System from any frontend framework. The system is production-ready with comprehensive authentication, role-based permissions, and full CRUD operations.
