# 🚀 FABI+ Quick Reference Guide

## 📋 Essential Commands

### Project Management
```bash
# Create new project
fabiplus project startproject myproject --orm sqlmodel

# With Docker support
fabiplus project startproject myproject --orm sqlmodel --docker

# List available ORMs
fabiplus project list-orms

# List project templates
fabiplus project list-templates
```

### App Management
```bash
# Create new app
fabiplus app startapp myapp

# Add model to app
fabiplus app addmodel MyModel --app myapp --fields "name:str,email:str,is_active:bool"

# Quick model creation
fabiplus app addmodel Post --fields "title:str,content:text,is_published:bool"
```

### Server Management
```bash
# Development server
fabiplus server run

# Production server
fabiplus server run --production

# Custom host/port
fabiplus server run --host 0.0.0.0 --port 8080

# Check server status
fabiplus server status
```

### Database Management
```bash
# Create migration (SQLModel/SQLAlchemy)
alembic revision --autogenerate -m "Add new models"

# Apply migrations
alembic upgrade head

# For Tortoise ORM
aerich init-db
aerich migrate
aerich upgrade
```

### Cache Management
```bash
# Clear cache
fabiplus cache clear

# List cached data
fabiplus cache list
```

---

## 🗄️ ORM Quick Reference

### SQLModel (Recommended)
```python
from sqlmodel import SQLModel, Field, Relationship
from fabiplus.core.models import BaseModel, register_model

@register_model
class User(BaseModel, table=True):
    name: str = Field(max_length=100)
    email: str = Field(unique=True)
    is_active: bool = Field(default=True)
    
    # Relationships
    posts: List["Post"] = Relationship(back_populates="author")
```

### SQLAlchemy
```python
from sqlalchemy import Column, String, Boolean, Integer
from fabiplus.core.models import BaseModel, register_model

@register_model
class User(BaseModel):
    __tablename__ = "users"
    
    name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
```

### Tortoise ORM
```python
from tortoise.models import Model
from tortoise import fields
from fabiplus.core.models import register_model

@register_model
class User(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=100)
    email = fields.CharField(max_length=255, unique=True)
    is_active = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)
```

---

## 📁 Media System Quick Reference

### File Upload
```python
from fabiplus.core.media.handlers import FileUploadHandler
from fabiplus.core.media.storage import LocalFileStorage, StorageConfig

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    storage_config = StorageConfig(base_path="media/uploads")
    storage = LocalFileStorage(storage_config)
    upload_handler = FileUploadHandler(storage, validator, processor)
    
    media_file = await upload_handler.upload_single_file(file=file, session=session)
    return {"id": media_file.id, "url": media_file.storage_url}
```

### File Validation
```python
from fabiplus.core.media.validators import ImageValidator, DocumentValidator

# Image validation
image_validator = ImageValidator(
    max_size=5*1024*1024,  # 5MB
    allowed_extensions=['.jpg', '.png', '.gif'],
    max_dimensions=(2000, 2000)
)

# Document validation
doc_validator = DocumentValidator(
    max_size=10*1024*1024,  # 10MB
    allowed_extensions=['.pdf', '.doc', '.docx']
)
```

---

## 🔐 Permissions Quick Reference

### Permission Decorators
```python
from fabiplus.core.permissions.decorators import (
    require_model_permission, require_row_permission, owner_required
)

# Model-level permission
@require_model_permission(PermissionAction.CREATE, model_class=Post)
async def create_post(post_data: PostCreate):
    pass

# Row-level permission
@require_row_permission(PermissionAction.UPDATE, instance_param="post")
async def update_post(post_id: int, post: Post = Depends(get_post)):
    pass

# Owner-only access
@owner_required(instance_param="post", owner_field="author_id")
async def delete_post(post_id: int, post: Post = Depends(get_post)):
    pass
```

### Permission Models
```python
from fabiplus.core.permissions.models import ModelPermission, FieldPermission

# Grant model permission
model_perm = ModelPermission(
    model_name="Post",
    user_id=user.id,
    can_create=True,
    can_read=True,
    can_update=True
)

# Grant field permission
field_perm = FieldPermission(
    model_name="Post",
    field_name="is_published",
    user_id=user.id,
    can_read=True,
    can_write=False  # Only admins can publish
)
```

---

## 📊 Response Types Quick Reference

### Streaming Responses
```python
from fabiplus.core.responses.streaming import (
    StreamingJSONResponse, StreamingCSVResponse
)

# Streaming JSON
@router.get("/export/json")
async def export_json():
    def data_generator():
        for item in large_dataset:
            yield item.dict()
    
    return StreamingJSONResponse(
        data_generator=data_generator(),
        chunk_size=100
    )

# CSV Export
@router.get("/export/csv")
async def export_csv():
    def data_generator():
        for item in dataset:
            yield {"id": item.id, "name": item.name}
    
    return StreamingCSVResponse(
        data_generator=data_generator(),
        filename="export.csv"
    )
```

### Format Responses
```python
from fabiplus.core.responses.formats import ExcelResponse, PDFResponse

# Excel export
@router.get("/export/excel")
async def export_excel():
    data = [{"id": 1, "name": "Item 1"}]
    return ExcelResponse(data=data, filename="export.xlsx")

# PDF generation
@router.get("/report/pdf")
async def generate_pdf():
    content = "Report content here..."
    return PDFResponse(content=content, filename="report.pdf")
```

### Pagination
```python
from fabiplus.core.responses.pagination import PaginatedResponse

@router.get("/items")
async def list_items(page: int = 1, per_page: int = 10):
    total_count = session.exec(select(func.count(Item.id))).one()
    offset = (page - 1) * per_page
    items = session.exec(select(Item).offset(offset).limit(per_page)).all()
    
    return PaginatedResponse(
        data=[item.dict() for item in items],
        page=page,
        per_page=per_page,
        total_items=total_count
    )
```

---

## 🎛️ Admin Interface Quick Reference

### Admin Configuration
```python
from fabiplus.core.admin import admin_site, ModelAdmin

@admin_site.register(Post)
class PostAdmin(ModelAdmin):
    list_display = ['title', 'author', 'is_published', 'created_at']
    list_filter = ['is_published', 'created_at']
    search_fields = ['title', 'content']
    
    fieldsets = [
        ('Basic Info', {'fields': ['title', 'slug']}),
        ('Content', {'fields': ['content', 'excerpt']}),
        ('Publishing', {'fields': ['is_published', 'published_at']}),
    ]
```

### Custom Admin Views
```python
@admin_site.register_view("/custom-report")
async def custom_report_view(request):
    # Custom admin view logic
    return {"template": "admin/custom_report.html", "context": {}}
```

---

## 🧪 Testing Quick Reference

### API Testing
```bash
# Test endpoints
curl http://localhost:8000/api/v1/posts/
curl -H "Authorization: Bearer TOKEN" http://localhost:8000/api/v1/posts/

# Test file upload
curl -X POST -F "file=@image.jpg" http://localhost:8000/api/v1/upload/

# Test exports
curl "http://localhost:8000/api/v1/posts/export/csv" -o posts.csv
```

### Unit Testing
```python
import pytest
from fastapi.testclient import TestClient

def test_create_post():
    with TestClient(app) as client:
        response = client.post(
            "/api/v1/posts/",
            json={"title": "Test", "content": "Content"},
            headers={"Authorization": "Bearer test-token"}
        )
        assert response.status_code == 201
```

---

## 🚀 Production Deployment

### Environment Variables
```bash
export SECRET_KEY="your-secret-key"
export DATABASE_URL="postgresql://user:pass@localhost/db"
export REDIS_URL="redis://localhost:6379"
export DEBUG=False
```

### Docker Commands
```bash
# Build and run
docker build -t myapp .
docker run -p 8000:8000 myapp

# With docker-compose
docker-compose up -d
docker-compose scale web=4
```

### Production Server
```bash
# FABI+ production server
fabiplus server run --production --host 0.0.0.0 --port 8000

# With Gunicorn
gunicorn myproject.wsgi:application --bind 0.0.0.0:8000 --workers 4
```

---

## 📚 Useful URLs

- **API Documentation:** http://localhost:8000/docs
- **Admin Interface:** http://localhost:8000/admin
- **API Root:** http://localhost:8000/api/v1/
- **Media Files:** http://localhost:8000/media/
- **Health Check:** http://localhost:8000/health

---

## 🔧 Common Patterns

### Model with Media
```python
@register_model
class Post(BaseModel, table=True):
    title: str
    content: str
    featured_image_id: Optional[int] = Field(foreign_key="media_files.id")
    
    # Relationship
    featured_image: Optional[MediaFile] = Relationship()
```

### Protected Endpoint
```python
@router.post("/posts/")
@require_model_permission(PermissionAction.CREATE, model_class=Post)
async def create_post(
    post_data: PostCreate,
    current_user = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    post = Post(**post_data.dict(), author_id=current_user.id)
    session.add(post)
    session.commit()
    return post
```

### File Upload with Processing
```python
@router.post("/upload-image/")
async def upload_image(file: UploadFile = File(...)):
    storage = LocalFileStorage(StorageConfig(base_path="media"))
    validator = ImageValidator(max_size=5*1024*1024)
    processor = ImageProcessor(thumbnail_sizes=[(150, 150), (300, 300)])
    
    handler = FileUploadHandler(storage, validator, processor)
    media_file = await handler.upload_single_file(file, session=session)
    
    return {
        "id": media_file.id,
        "url": media_file.storage_url,
        "thumbnails": media_file.thumbnails
    }
```

---

*This quick reference covers the most commonly used FABI+ features. For detailed documentation, see the full getting started guide.*
