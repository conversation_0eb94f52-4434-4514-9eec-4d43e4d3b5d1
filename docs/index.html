<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FABI+ Framework - Complete Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #343a40;
        }
        
        body {
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            padding: 4rem 0;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        
        .code-block {
            background: #2d3748;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .sidebar {
            position: sticky;
            top: 20px;
            height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .nav-link {
            color: var(--dark-color);
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin-bottom: 0.25rem;
        }
        
        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .section {
            padding: 2rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .badge-feature {
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }
        
        .command-box {
            background: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .step-number {
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .orm-comparison {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✅";
            margin-right: 0.5rem;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-rocket"></i> FABI+
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#installation">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#quickstart">Quick Start</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#examples">Examples</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://github.com/Helevon-Technologies-LTD/fabiplus" target="_blank">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        FABI+ Framework
                        <span class="badge-feature">v1.0</span>
                    </h1>
                    <p class="lead mb-4">
                        The most comprehensive Python API framework combining the best of FastAPI and Django. 
                        Build production-ready APIs with advanced features out of the box.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#installation" class="btn btn-light btn-lg">
                            <i class="fas fa-download"></i> Get Started
                        </a>
                        <a href="#examples" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-code"></i> View Examples
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="code-block">
                        <pre><code class="language-bash"># Install FABI+
pip install fabiplus

# Create a new project
fabiplus project startproject myblog --orm sqlmodel

# Create an app
cd myblog
fabiplus app startapp blog

# Add models
fabiplus app addmodel Post --fields "title:str,content:text"

# Run the server
fabiplus server run</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Overview -->
    <section class="section bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Why Choose FABI+?</h2>
                    <p class="lead">Production-ready features that would take months to build from scratch</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-database fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Multi-ORM Support</h5>
                            <p class="card-text">Choose between SQLModel, SQLAlchemy, or Tortoise ORM based on your needs.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-file-upload fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Advanced Media System</h5>
                            <p class="card-text">File uploads, processing, thumbnails, and streaming with built-in validation.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Granular Permissions</h5>
                            <p class="card-text">Model, field, and row-level access control with ownership and conditions.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-stream fa-3x text-info mb-3"></i>
                            <h5 class="card-title">Response Streaming</h5>
                            <p class="card-text">Handle large datasets with streaming JSON, CSV, Excel, and PDF responses.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 bg-light">
                <div class="sidebar p-3">
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#installation">
                            <i class="fas fa-download"></i> Installation
                        </a>
                        <a class="nav-link" href="#project-creation">
                            <i class="fas fa-plus-circle"></i> Project Creation
                        </a>
                        <a class="nav-link" href="#orm-choice">
                            <i class="fas fa-database"></i> ORM Choice
                        </a>
                        <a class="nav-link" href="#app-creation">
                            <i class="fas fa-cube"></i> App Creation
                        </a>
                        <a class="nav-link" href="#models">
                            <i class="fas fa-table"></i> Models & Migrations
                        </a>
                        <a class="nav-link" href="#media-system">
                            <i class="fas fa-file-upload"></i> Media System
                        </a>
                        <a class="nav-link" href="#permissions">
                            <i class="fas fa-shield-alt"></i> Permissions
                        </a>
                        <a class="nav-link" href="#responses">
                            <i class="fas fa-stream"></i> Response Types
                        </a>
                        <a class="nav-link" href="#admin">
                            <i class="fas fa-cog"></i> Admin Interface
                        </a>
                        <a class="nav-link" href="#testing">
                            <i class="fas fa-vial"></i> Testing
                        </a>
                        <a class="nav-link" href="#deployment">
                            <i class="fas fa-rocket"></i> Deployment
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9 col-xl-10">
                <div class="p-4">
                    <!-- Installation Section -->
                    <section id="installation" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">1</span>
                            Installation & Setup
                        </h2>
                        
                        <div class="info-box">
                            <h5><i class="fas fa-info-circle"></i> Prerequisites</h5>
                            <ul>
                                <li>Python 3.10 or higher</li>
                                <li>Poetry (recommended) or pip</li>
                                <li>Git (for cloning the repository)</li>
                            </ul>
                        </div>

                        <h4>Install FABI+</h4>
                        <div class="command-box">
                            <h6><i class="fas fa-terminal"></i> Installation Commands</h6>
                            <pre><code class="language-bash"># Clone the repository
git clone https://github.com/Helevon-Technologies-LTD/fabiplus.git
cd fabiplus

# Install with Poetry (recommended)
poetry install

# Or install with pip
pip install -e .

# Verify installation
fabiplus --help</code></pre>
                        </div>

                        <h4>Available Commands</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Project Management</h6>
                                <ul class="feature-list">
                                    <li><code>fabiplus project startproject</code> - Create new project</li>
                                    <li><code>fabiplus project list-templates</code> - List templates</li>
                                    <li><code>fabiplus project list-orms</code> - List ORMs</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>App Management</h6>
                                <ul class="feature-list">
                                    <li><code>fabiplus app startapp</code> - Create new app</li>
                                    <li><code>fabiplus app addmodel</code> - Add model</li>
                                    <li><code>fabiplus server run</code> - Start server</li>
                                </ul>
                            </div>
                        </div>
                    </section>

                    <!-- Project Creation Section -->
                    <section id="project-creation" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">2</span>
                            Project Creation with ORM Choice
                        </h2>

                        <p class="lead">FABI+ supports three powerful ORMs. Choose the one that fits your project needs:</p>

                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="orm-comparison">
                                    <h5 class="text-primary"><i class="fas fa-star"></i> SQLModel (Recommended)</h5>
                                    <ul class="feature-list">
                                        <li>Type safety with Pydantic</li>
                                        <li>FastAPI integration</li>
                                        <li>Async support</li>
                                        <li>SQLAlchemy power</li>
                                    </ul>
                                    <div class="command-box">
                                        <code>fabiplus project startproject myblog --orm sqlmodel</code>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="orm-comparison">
                                    <h5 class="text-success"><i class="fas fa-database"></i> SQLAlchemy</h5>
                                    <ul class="feature-list">
                                        <li>Mature ecosystem</li>
                                        <li>Complex queries</li>
                                        <li>Full ORM power</li>
                                        <li>Async support (2.0+)</li>
                                    </ul>
                                    <div class="command-box">
                                        <code>fabiplus project startproject myblog --orm sqlalchemy</code>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="orm-comparison">
                                    <h5 class="text-info"><i class="fas fa-bolt"></i> Tortoise ORM</h5>
                                    <ul class="feature-list">
                                        <li>Async-first design</li>
                                        <li>Django-like syntax</li>
                                        <li>Built-in migrations</li>
                                        <li>High performance</li>
                                    </ul>
                                    <div class="command-box">
                                        <code>fabiplus project startproject myblog --orm tortoise</code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4>Project Structure</h4>
                        <div class="code-block">
                            <pre><code>myblog/
├── myblog/
│   ├── __init__.py
│   ├── settings.py          # Project settings
│   ├── urls.py             # URL routing
│   └── wsgi.py             # WSGI application
├── apps/
│   └── core/               # Default core app
│       ├── models.py       # Database models
│       ├── views.py        # API views
│       ├── admin.py        # Admin configuration
│       └── urls.py         # App URLs
├── migrations/             # Database migrations
├── media/                  # Media files storage
├── static/                 # Static files
├── templates/              # Jinja2 templates
├── pyproject.toml          # Dependencies (ORM-specific)
└── README.md</code></pre>
                        </div>
                    </section>

                    <!-- App Creation Section -->
                    <section id="app-creation" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">3</span>
                            Creating Your First App
                        </h2>

                        <div class="success-box">
                            <h5><i class="fas fa-lightbulb"></i> Tutorial: Building a Blog App</h5>
                            <p>We'll create a complete blog application to showcase all FABI+ features.</p>
                        </div>

                        <h4>Step 1: Navigate to Your Project</h4>
                        <div class="command-box">
                            <code>cd myblog</code>
                        </div>

                        <h4>Step 2: Create the Blog App</h4>
                        <div class="command-box">
                            <code>fabiplus app startapp blog</code>
                        </div>

                        <h4>Step 3: Add Models</h4>
                        <div class="command-box">
                            <pre><code># Quick model creation with CLI
fabiplus app addmodel Category --app blog --fields "name:str,slug:str,description:text"
fabiplus app addmodel Post --app blog --fields "title:str,slug:str,content:text,is_published:bool"
fabiplus app addmodel Tag --app blog --fields "name:str,color:str"</code></pre>
                        </div>

                        <h4>Step 4: Register the App</h4>
                        <p>Edit <code>myblog/settings.py</code>:</p>
                        <div class="code-block">
                            <pre><code class="language-python">INSTALLED_APPS = [
    "apps.core",
    "apps.blog",  # Add this line
]</code></pre>
                        </div>
                    </section>

                    <!-- Models Section -->
                    <section id="models" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">4</span>
                            Models & Database Setup
                        </h2>

                        <h4>SQLModel Example (Recommended)</h4>
                        <div class="code-block">
                            <pre><code class="language-python">from typing import Optional, List
from datetime import datetime
from sqlmodel import SQLModel, Field, Relationship
from fabiplus.core.models import BaseModel, register_model

@register_model
class Category(BaseModel, table=True):
    """Blog category model"""
    __tablename__ = "blog_categories"

    name: str = Field(max_length=100, description="Category name")
    slug: str = Field(max_length=100, unique=True, description="URL slug")
    description: Optional[str] = Field(default="", description="Category description")
    is_active: bool = Field(default=True, description="Is category active")

    # Relationships
    posts: List["Post"] = Relationship(back_populates="category")

    class Config:
        _verbose_name = "Category"
        _verbose_name_plural = "Categories"

    def __str__(self):
        return self.name

@register_model
class Post(BaseModel, table=True):
    """Blog post model"""
    __tablename__ = "blog_posts"

    title: str = Field(max_length=200, description="Post title")
    slug: str = Field(max_length=200, unique=True, description="URL slug")
    content: str = Field(description="Post content")
    excerpt: Optional[str] = Field(default="", description="Post excerpt")

    # Status
    is_published: bool = Field(default=False, description="Is post published")
    is_featured: bool = Field(default=False, description="Is post featured")

    # Foreign Keys
    category_id: Optional[int] = Field(default=None, foreign_key="blog_categories.id")

    # Relationships
    category: Optional[Category] = Relationship(back_populates="posts")

    def __str__(self):
        return self.title</code></pre>
                        </div>

                        <h4>Database Migrations</h4>
                        <div class="command-box">
                            <pre><code># Initialize migrations (SQLModel/SQLAlchemy)
alembic init migrations

# Create migration
alembic revision --autogenerate -m "Add blog models"

# Apply migrations
alembic upgrade head

# For Tortoise ORM
aerich init -t myblog.settings.TORTOISE_ORM
aerich init-db</code></pre>
                        </div>

                        <h4>Start the Server</h4>
                        <div class="command-box">
                            <pre><code># Development server
fabiplus server run

# Production server
fabiplus server run --production

# Custom host/port
fabiplus server run --host 0.0.0.0 --port 8080</code></pre>
                        </div>

                        <div class="info-box">
                            <h5><i class="fas fa-globe"></i> Access Your Application</h5>
                            <ul>
                                <li><strong>API:</strong> <a href="http://localhost:8000/api/v1/" target="_blank">http://localhost:8000/api/v1/</a></li>
                                <li><strong>Documentation:</strong> <a href="http://localhost:8000/docs" target="_blank">http://localhost:8000/docs</a></li>
                                <li><strong>Admin:</strong> <a href="http://localhost:8000/admin" target="_blank">http://localhost:8000/admin</a></li>
                            </ul>
                        </div>
                    </section>

                    <!-- Media System Section -->
                    <section id="media-system" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">5</span>
                            Advanced Media System
                        </h2>

                        <p class="lead">FABI+ includes a comprehensive media system for file uploads, processing, and serving.</p>

                        <h4>Features</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="feature-list">
                                    <li>File validation (size, type, content)</li>
                                    <li>Image processing & thumbnails</li>
                                    <li>Chunked uploads for large files</li>
                                    <li>Multiple storage backends</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="feature-list">
                                    <li>Automatic metadata extraction</li>
                                    <li>Access control & permissions</li>
                                    <li>Streaming downloads</li>
                                    <li>Range request support</li>
                                </ul>
                            </div>
                        </div>

                        <h4>Basic File Upload</h4>
                        <div class="code-block">
                            <pre><code class="language-python">from fastapi import UploadFile, File
from fabiplus.core.media.handlers import FileUploadHandler
from fabiplus.core.media.storage import LocalFileStorage, StorageConfig

@router.post("/upload-image")
async def upload_image(
    file: UploadFile = File(...),
    session: Session = Depends(get_session)
):
    # Setup storage
    storage_config = StorageConfig(base_path="media/blog")
    storage = LocalFileStorage(storage_config)

    # Upload file
    upload_handler = FileUploadHandler(storage, validator, processor)
    media_file = await upload_handler.upload_single_file(
        file=file,
        session=session
    )

    return {
        "id": media_file.id,
        "url": media_file.storage_url,
        "thumbnails": media_file.thumbnails
    }</code></pre>
                        </div>

                        <h4>Test Upload</h4>
                        <div class="command-box">
                            <pre><code># Test file upload
curl -X POST "http://localhost:8000/api/v1/blog/upload-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test-image.jpg"</code></pre>
                        </div>
                    </section>

                    <!-- Permissions Section -->
                    <section id="permissions" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">6</span>
                            Advanced Permissions System
                        </h2>

                        <p class="lead">Granular access control at model, field, and row levels with ownership and conditions.</p>

                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5><i class="fas fa-table"></i> Model-Level</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="feature-list">
                                            <li>Create permissions</li>
                                            <li>Read permissions</li>
                                            <li>Update permissions</li>
                                            <li>Delete permissions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5><i class="fas fa-columns"></i> Field-Level</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="feature-list">
                                            <li>Field read access</li>
                                            <li>Field write access</li>
                                            <li>Sensitive data masking</li>
                                            <li>Conditional access</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-warning text-white">
                                        <h5><i class="fas fa-row"></i> Row-Level</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="feature-list">
                                            <li>Ownership-based access</li>
                                            <li>Instance permissions</li>
                                            <li>Dynamic conditions</li>
                                            <li>Expiring permissions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4>Permission Decorators</h4>
                        <div class="code-block">
                            <pre><code class="language-python">from fabiplus.core.permissions.decorators import (
    require_model_permission, require_row_permission, owner_required
)

@router.post("/posts/")
@require_model_permission(PermissionAction.CREATE, model_class=Post)
async def create_post(post_data: PostCreate, current_user=Depends(get_current_user)):
    """Create a new blog post"""
    post = Post(**post_data.dict(), author_id=current_user.id)
    session.add(post)
    session.commit()
    return post

@router.put("/posts/{post_id}")
@owner_required(instance_param="post", owner_field="author_id")
async def update_post(post_id: int, post_data: PostUpdate):
    """Update post (only by owner)"""
    # Update logic here
    pass</code></pre>
                        </div>
                    </section>

                    <!-- Response Types Section -->
                    <section id="responses" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">7</span>
                            Response Types & Streaming
                        </h2>

                        <p class="lead">Handle large datasets efficiently with streaming responses and multiple export formats.</p>

                        <div class="row g-3">
                            <div class="col-md-6 col-lg-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-code fa-2x text-primary mb-2"></i>
                                        <h6>Streaming JSON</h6>
                                        <small>Large datasets</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-csv fa-2x text-success mb-2"></i>
                                        <h6>CSV Export</h6>
                                        <small>Data analysis</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-excel fa-2x text-warning mb-2"></i>
                                        <h6>Excel Export</h6>
                                        <small>Business reports</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                        <h6>PDF Reports</h6>
                                        <small>Documents</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4>Streaming JSON Response</h4>
                        <div class="code-block">
                            <pre><code class="language-python">from fabiplus.core.responses.streaming import StreamingJSONResponse

@router.get("/posts/export/json")
async def export_posts_json():
    """Export all posts as streaming JSON"""

    async def post_generator():
        offset = 0
        limit = 100

        while True:
            posts = session.exec(select(Post).offset(offset).limit(limit)).all()
            if not posts:
                break

            for post in posts:
                yield {
                    "id": post.id,
                    "title": post.title,
                    "content": post.content,
                    "created_at": post.created_at.isoformat()
                }
            offset += limit

    return StreamingJSONResponse(
        data_generator=post_generator(),
        chunk_size=100
    )</code></pre>
                        </div>

                        <h4>CSV Export</h4>
                        <div class="code-block">
                            <pre><code class="language-python">from fabiplus.core.responses.streaming import StreamingCSVResponse

@router.get("/posts/export/csv")
async def export_posts_csv():
    """Export posts as CSV"""

    def post_generator():
        posts = session.exec(select(Post)).all()
        for post in posts:
            yield {
                "ID": post.id,
                "Title": post.title,
                "Published": "Yes" if post.is_published else "No",
                "Created": post.created_at.strftime("%Y-%m-%d")
            }

    return StreamingCSVResponse(
        data_generator=post_generator(),
        filename="blog_posts.csv"
    )</code></pre>
                        </div>
                    </section>

                    <!-- Admin Interface Section -->
                    <section id="admin" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">8</span>
                            Admin Interface
                        </h2>

                        <p class="lead">Django-style admin interface with HTMX for modern, responsive management.</p>

                        <div class="info-box">
                            <h5><i class="fas fa-globe"></i> Access Admin Interface</h5>
                            <p>Visit <a href="http://localhost:8000/admin" target="_blank">http://localhost:8000/admin</a> after starting your server.</p>
                        </div>

                        <h4>Admin Configuration</h4>
                        <div class="code-block">
                            <pre><code class="language-python"># apps/blog/admin.py
from fabiplus.core.admin import admin_site, ModelAdmin

@admin_site.register(Post)
class PostAdmin(ModelAdmin):
    list_display = ['title', 'category', 'is_published', 'created_at']
    list_filter = ['is_published', 'category', 'created_at']
    search_fields = ['title', 'content']</code></pre>
                        </div>
                    </section>

                    <!-- Testing Section -->
                    <section id="testing" class="section">
                        <h2 class="display-6 fw-bold mb-4">
                            <span class="step-number">9</span>
                            Testing Your Application
                        </h2>

                        <h4>API Testing</h4>
                        <div class="command-box">
                            <pre><code># Test basic endpoints
curl http://localhost:8000/api/v1/blog/posts/
curl http://localhost:8000/api/v1/blog/categories/

# Test CSV export
curl "http://localhost:8000/api/v1/blog/posts/export/csv" -o posts.csv</code></pre>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>FABI+ Framework</h5>
                    <p>The most comprehensive Python API framework for building production-ready applications.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Links</h5>
                    <a href="https://github.com/Helevon-Technologies-LTD/fabiplus" class="text-white me-3">
                        <i class="fab fa-github"></i> GitHub
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
