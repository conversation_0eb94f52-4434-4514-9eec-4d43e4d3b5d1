# FABI+ Blog System - Production Deployment Guide

## 🚀 Production-Ready Features

The FABI+ Blog System includes all essential features for production deployment:

### ✅ **Authentication & Security**
- OAuth2 JWT token authentication
- User registration with validation
- Role-based access control (Author, Editor, Moderator, Admin)
- Password hashing with bcrypt
- CORS support
- Rate limiting capabilities

### ✅ **Blog Functionality**
- Complete CRUD operations for posts, categories, tags, comments
- Public/private content management
- Featured posts and recent posts
- Comment moderation system
- View tracking
- SEO-friendly slugs

### ✅ **Admin Features**
- Admin dashboard with statistics
- User role management
- Content moderation
- Permission management
- Activity logging

### ✅ **API Features**
- RESTful API with OpenAPI documentation
- Automatic API route generation
- Comprehensive error handling
- Response pagination
- Data validation

## 🏗️ Deployment Architecture

### Recommended Stack
```
Frontend (React/Vue/Angular) 
    ↓
Reverse Proxy (Nginx)
    ↓
FABI+ Application (Python/FastAPI)
    ↓
Database (PostgreSQL/MySQL)
    ↓
Cache (Redis - Optional)
```

## 🐳 Docker Deployment

### 1. Create Dockerfile

```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run application
CMD ["fabiplus", "server", "run", "--production", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Create docker-compose.yml

```yaml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: fabiplus_blog
      POSTGRES_USER: fabiplus
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  app:
    build: .
    environment:
      - DATABASE_URL=postgresql://fabiplus:${DB_PASSWORD}@db:5432/fabiplus_blog
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENVIRONMENT=production
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Environment Configuration

Create `.env` file:
```bash
# Database
DB_PASSWORD=your_secure_db_password
DATABASE_URL=*****************************************************/fabiplus_blog

# JWT
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application
ENVIRONMENT=production
DEBUG=false
CORS_ENABLED=true
CORS_ORIGINS=["https://yourdomain.com"]

# Security
SECURITY_ENABLED=true
RATE_LIMITING_ENABLED=true

# Admin
ADMIN_ROUTES_IN_DOCS=false

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/fabiplus.log
```

## 🌐 Nginx Configuration

### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # Security Headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        # API Routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Auth Routes (stricter rate limiting)
        location /auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Documentation
        location /docs {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files (if serving frontend)
        location / {
            root /var/www/html;
            try_files $uri $uri/ /index.html;
        }
    }
}
```

## 🗄️ Database Setup

### PostgreSQL Production Setup

```sql
-- Create database and user
CREATE DATABASE fabiplus_blog;
CREATE USER fabiplus WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE fabiplus_blog TO fabiplus;

-- Connect to the database
\c fabiplus_blog;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO fabiplus;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO fabiplus;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO fabiplus;
```

### Database Migration

```bash
# Run migrations
docker-compose exec app fabiplus db migrate

# Create superuser
docker-compose exec app fabiplus user create \
  --username admin \
  --email <EMAIL> \
  --password secure_admin_password \
  --superuser

# Create staff users
docker-compose exec app fabiplus user create \
  --username editor \
  --email <EMAIL> \
  --password secure_editor_password \
  --staff
```

## 🔒 Security Checklist

### Application Security
- [ ] Change default JWT secret key
- [ ] Use strong database passwords
- [ ] Enable HTTPS with valid SSL certificates
- [ ] Configure CORS for your domain only
- [ ] Enable rate limiting
- [ ] Set up proper logging
- [ ] Configure firewall rules
- [ ] Regular security updates

### Database Security
- [ ] Use encrypted connections
- [ ] Restrict database access
- [ ] Regular backups
- [ ] Monitor for suspicious activity

### Server Security
- [ ] Keep OS updated
- [ ] Configure fail2ban
- [ ] Use non-root user for application
- [ ] Disable unnecessary services
- [ ] Monitor system resources

## 📊 Monitoring & Logging

### Application Monitoring

```python
# Add to settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/fabiplus.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### Health Check Endpoint

```python
# Add to your app
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }
```

## 🚀 Deployment Commands

### Initial Deployment

```bash
# Clone repository
git clone https://github.com/yourusername/your-blog-project.git
cd your-blog-project

# Set up environment
cp .env.example .env
# Edit .env with your production values

# Build and start services
docker-compose up -d --build

# Run migrations
docker-compose exec app fabiplus db migrate

# Create admin user
docker-compose exec app fabiplus user create --username admin --email <EMAIL> --password your_password --superuser

# Check logs
docker-compose logs -f app
```

### Updates and Maintenance

```bash
# Update application
git pull origin main
docker-compose build app
docker-compose up -d app

# Database backup
docker-compose exec db pg_dump -U fabiplus fabiplus_blog > backup_$(date +%Y%m%d_%H%M%S).sql

# View logs
docker-compose logs -f app

# Monitor resources
docker stats
```

## 🔧 Performance Optimization

### Database Optimization
- Add database indexes for frequently queried fields
- Use connection pooling
- Regular VACUUM and ANALYZE operations
- Monitor slow queries

### Application Optimization
- Enable Redis caching
- Use CDN for static assets
- Implement response compression
- Monitor memory usage

### Nginx Optimization
- Enable gzip compression
- Set proper cache headers
- Use HTTP/2
- Optimize worker processes

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer (HAProxy/AWS ALB)
- Multiple application instances
- Database read replicas
- Shared session storage (Redis)

### Vertical Scaling
- Increase server resources
- Optimize database configuration
- Tune application settings

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   docker-compose exec app python -c "from fabiplus.core.models import ModelRegistry; print('DB OK')"
   ```

2. **Authentication Problems**
   ```bash
   # Verify JWT configuration
   docker-compose exec app python -c "import os; print(os.getenv('JWT_SECRET_KEY'))"
   ```

3. **Permission Issues**
   ```bash
   # Check file permissions
   docker-compose exec app ls -la /app
   ```

### Log Analysis
```bash
# Application logs
docker-compose logs app | grep ERROR

# Database logs
docker-compose logs db | grep ERROR

# Nginx logs
docker-compose logs nginx | grep ERROR
```

## 📞 Support

For production support and advanced configuration:
- Check the FABI+ documentation
- Review application logs
- Monitor system resources
- Set up alerting for critical issues

The FABI+ Blog System is production-ready with comprehensive features, security measures, and deployment options suitable for enterprise use.
