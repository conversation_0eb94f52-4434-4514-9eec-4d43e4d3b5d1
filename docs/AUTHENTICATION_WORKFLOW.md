# FABI+ Authentication & Permission Workflow Guide

## Complete Workflow Overview

This guide explains the complete workflow for implementing model authentication and view authentication in FABI+, from setup to testing.

## 🚀 Quick Start Workflow

### 1. Project Setup

```bash
# Create a new FABI+ project
fabiplus project startproject myproject

# Navigate to project directory
cd myproject

# Run database migrations
fabiplus db migrate

# Create a superuser
fabiplus user create --username admin --email <EMAIL> --password admin123 --superuser

# Create a regular user for testing
fabiplus user create --username testuser --email <EMAIL> --password testpass123
```

### 2. Model Authentication Setup

#### Option A: Authenticated Model (Requires Login)

```python
# apps/yourapp/models.py
from sqlmodel import SQLModel, Field
from typing import Optional
import uuid
from datetime import datetime

class YourModel(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str
    description: Optional[str] = None
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[uuid.UUID] = Field(foreign_key="user.id")

# apps/yourapp/views.py
from fabiplus.core.views import AuthenticatedGenericAPIView
from .models import YourModel

class YourModelView(AuthenticatedGenericAPIView):
    model = YourModel
    # This automatically requires authentication for all CRUD operations

# apps/yourapp/__init__.py
from fabiplus.core.registry import ModelRegistry
from .models import YourModel
from .views import YourModelView

# Register the model with authentication
ModelRegistry.register_model(
    YourModel,
    view_class=YourModelView,
    tags=["Your App"]
)
```

#### Option B: Public Model (No Authentication Required)

```python
# apps/yourapp/views.py
from fabiplus.core.views import GenericAPIView
from .models import YourModel

class YourModelView(GenericAPIView):
    model = YourModel
    # This allows public access to all CRUD operations

# Register the model without authentication
ModelRegistry.register_model(
    YourModel,
    view_class=YourModelView,
    tags=["Public"]
)
```

#### Option C: Mixed Authentication (Some endpoints protected, some public)

```python
# apps/yourapp/views.py
from fabiplus.core.views import GenericAPIView
from fabiplus.core.auth import get_current_active_user
from fastapi import Depends, HTTPException

class MixedModelView(GenericAPIView):
    model = YourModel
    
    def list(self, session, *args, **kwargs):
        # Public endpoint - anyone can list
        return super().list(session, *args, **kwargs)
    
    def retrieve(self, item_id, session):
        # Public endpoint - anyone can view individual items
        return super().retrieve(item_id, session)
    
    def create(self, data, session, current_user = Depends(get_current_active_user)):
        # Protected endpoint - requires authentication
        if not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Add user information to data
        data['created_by'] = current_user.id
        return super().create(data, session)
    
    def update(self, item_id, data, session, current_user = Depends(get_current_active_user)):
        # Protected endpoint - requires authentication
        if not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        return super().update(item_id, data, session)
    
    def delete(self, item_id, session, current_user = Depends(get_current_active_user)):
        # Protected endpoint - requires authentication
        if not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        return super().delete(item_id, session)
```

### 3. Custom Permission Logic

```python
# apps/yourapp/views.py
from fabiplus.core.views import AuthenticatedGenericAPIView
from fabiplus.core.permissions.enums import PermissionAction
from fastapi import HTTPException, status

class CustomPermissionView(AuthenticatedGenericAPIView):
    model = YourModel
    
    def list(self, session, *args, current_user=None, **kwargs):
        # Custom permission check
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is inactive"
            )
        
        # Staff can see all records
        if current_user.is_staff:
            return super().list(session, *args, current_user=current_user, **kwargs)
        
        # Regular users see only their own records
        kwargs['filters'] = {'created_by': current_user.id}
        return super().list(session, *args, current_user=current_user, **kwargs)
    
    def create(self, data, session, current_user=None):
        # Only staff can create records
        if not current_user.is_staff:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Staff access required"
            )
        
        # Automatically set creator
        data['created_by'] = current_user.id
        return super().create(data, session, current_user=current_user)
    
    def update(self, item_id, data, session, current_user=None):
        # Users can only update their own records
        item = session.get(self.model, item_id)
        if not item:
            raise HTTPException(status_code=404, detail="Item not found")
        
        if not current_user.is_staff and item.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only update your own records"
            )
        
        return super().update(item_id, data, session, current_user=current_user)
```

### 4. Start the Server

```bash
# Start development server
fabiplus server run

# Server will be available at http://127.0.0.1:8000
# API documentation at http://127.0.0.1:8000/docs
```

## 🔐 Authentication Flow

### 1. Client Authentication

```python
import requests

# Step 1: Get authentication token
response = requests.post("http://127.0.0.1:8000/auth/token", data={
    "username": "admin",
    "password": "admin123"
})

if response.status_code == 200:
    token = response.json()["access_token"]
    print(f"✅ Authentication successful")
else:
    print(f"❌ Authentication failed: {response.status_code}")
```

### 2. Using Authentication Token

```python
# Step 2: Use token in subsequent requests
headers = {"Authorization": f"Bearer {token}"}

# Make authenticated requests
response = requests.get("http://127.0.0.1:8000/api/v1/yourmodel", headers=headers)

if response.status_code == 200:
    data = response.json()
    print(f"✅ Retrieved {data.get('total', 0)} items")
elif response.status_code == 401:
    print("❌ Authentication required")
elif response.status_code == 403:
    print("❌ Permission denied")
```

### 3. Current User Information

```python
# Get current user information
response = requests.get("http://127.0.0.1:8000/auth/me", headers=headers)

if response.status_code == 200:
    user = response.json()
    print(f"Logged in as: {user['username']}")
    print(f"Is staff: {user['is_staff']}")
    print(f"Is superuser: {user['is_superuser']}")
```

## 🛡️ Permission System Workflow

### 1. Model-Level Permissions

```python
# Create model permission for a user
permission_data = {
    "model_name": "yourmodel",
    "user_id": "user-uuid",
    "role_id": None,
    "can_create": True,
    "can_read": True,
    "can_update": False,
    "can_delete": False,
    "can_list": True,
    "conditions": "{}",
    "is_active": True
}

response = requests.post(
    "http://127.0.0.1:8000/api/v1/modelpermission",
    json=permission_data,
    headers=admin_headers
)
```

### 2. Field-Level Permissions

```python
# Create field permission
field_permission_data = {
    "model_name": "yourmodel",
    "field_name": "sensitive_field",
    "user_id": "user-uuid",
    "role_id": None,
    "can_read": True,
    "can_write": False,
    "can_admin": False,
    "is_sensitive": True,
    "mask_value": True,
    "conditions": "{}"
}

response = requests.post(
    "http://127.0.0.1:8000/api/v1/fieldpermission",
    json=field_permission_data,
    headers=admin_headers
)
```

### 3. Row-Level Permissions

```python
# Create row permission
row_permission_data = {
    "model_name": "yourmodel",
    "row_id": "record-uuid",
    "user_id": "user-uuid",
    "role_id": None,
    "can_read": True,
    "can_update": False,
    "can_delete": False,
    "can_admin": False,
    "is_owner": False,
    "owner_field": "created_by",
    "conditions": "{}"
}

response = requests.post(
    "http://127.0.0.1:8000/api/v1/rowpermission",
    json=row_permission_data,
    headers=admin_headers
)
```

## 🧪 Testing Workflow

### 1. Run Simple Permission Test

```bash
cd tests
python test_simple_permissions.py
```

### 2. Run Comprehensive Test Suite

```bash
cd tests
python test_fabiplus_comprehensive.py
```

### 3. Manual Testing Steps

1. **Test Unauthenticated Access**:
   ```bash
   curl http://127.0.0.1:8000/api/v1/yourmodel
   # Should return 401 Unauthorized
   ```

2. **Test Authentication**:
   ```bash
   curl -X POST http://127.0.0.1:8000/auth/token \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=admin&password=admin123"
   # Should return JWT token
   ```

3. **Test Authenticated Access**:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://127.0.0.1:8000/api/v1/yourmodel
   # Should return data if user has permissions
   ```

## 📋 Common Patterns

### 1. Owner-Only Access Pattern

```python
class OwnerOnlyView(AuthenticatedGenericAPIView):
    model = YourModel
    
    def list(self, session, *args, current_user=None, **kwargs):
        # Users see only their own records
        kwargs['filters'] = {'created_by': current_user.id}
        return super().list(session, *args, current_user=current_user, **kwargs)
    
    def retrieve(self, item_id, session, current_user=None):
        item = session.get(self.model, item_id)
        if not item or item.created_by != current_user.id:
            raise HTTPException(status_code=404, detail="Item not found")
        return super().retrieve(item_id, session, current_user=current_user)
```

### 2. Role-Based Access Pattern

```python
class RoleBasedView(AuthenticatedGenericAPIView):
    model = YourModel
    
    def create(self, data, session, current_user=None):
        # Only managers can create
        if not self.has_role(current_user, "manager"):
            raise HTTPException(status_code=403, detail="Manager role required")
        return super().create(data, session, current_user=current_user)
    
    def has_role(self, user, role_name):
        # Check if user has specific role
        # Implementation depends on your role system
        return user.is_staff  # Simplified example
```

### 3. Hierarchical Access Pattern

```python
class HierarchicalView(AuthenticatedGenericAPIView):
    model = YourModel
    
    def list(self, session, *args, current_user=None, **kwargs):
        if current_user.is_superuser:
            # Superuser sees everything
            return super().list(session, *args, current_user=current_user, **kwargs)
        elif current_user.is_staff:
            # Staff sees active records
            kwargs['filters'] = {'is_active': True}
            return super().list(session, *args, current_user=current_user, **kwargs)
        else:
            # Regular users see only their own records
            kwargs['filters'] = {'created_by': current_user.id, 'is_active': True}
            return super().list(session, *args, current_user=current_user, **kwargs)
```

## 🔧 Troubleshooting

### Common Issues and Solutions

1. **401 Unauthorized**
   - Check if token is included in Authorization header
   - Verify token format: `Bearer <token>`
   - Check if token has expired

2. **403 Forbidden**
   - User is authenticated but lacks permissions
   - Check user roles and permissions in database
   - Verify permission logic in view

3. **500 Internal Server Error**
   - Check server logs for detailed error
   - Verify model registration
   - Check database connection

### Debug Commands

```bash
# Check user permissions
fabiplus user list

# Check server logs
fabiplus server run --debug

# Test specific endpoint
curl -v -H "Authorization: Bearer TOKEN" http://127.0.0.1:8000/api/v1/model
```

## 📚 Next Steps

1. **Implement Custom Permissions**: Create specific permission logic for your use case
2. **Add Role Management**: Implement role-based access control
3. **Setup Field-Level Security**: Protect sensitive fields
4. **Implement Audit Logging**: Track user actions
5. **Add Rate Limiting**: Protect against abuse
6. **Setup HTTPS**: Secure production deployment

This workflow provides a complete foundation for implementing secure, authenticated APIs with FABI+.
