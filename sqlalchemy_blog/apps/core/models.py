"""
Core Models
Database models for the core app (SQLAlchemy backend)
"""

from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base

from fabiplus.core.models import BaseModel, register_model

Base = declarative_base()


# Add your models here
# Example:
# @register_model
# class MyModel(Base):
#     """Example model"""
#     __tablename__ = "core_mymodel"
#
#     id = Column(Integer, primary_key=True, index=True)
#     name = Column(String(100), nullable=False, comment="Name")
#     description = Column(Text, nullable=True, default="", comment="Description")
#     is_active = Column(Boolean, default=True, comment="Is active")
#
#     def __str__(self):
#         return self.name
