"""
Core Serializers
Pydantic schemas for the core app
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
import uuid


class CoreItemBase(BaseModel):
    """Base schema for CoreItem"""
    name: str = Field(..., max_length=100, description="Item name")
    description: Optional[str] = Field("", description="Item description")
    is_active: bool = Field(True, description="Is item active")


class CoreItemCreate(CoreItemBase):
    """Schema for creating CoreItem"""
    pass


class CoreItemUpdate(CoreItemBase):
    """Schema for updating CoreItem"""
    name: Optional[str] = Field(None, max_length=100, description="Item name")
    is_active: Optional[bool] = Field(None, description="Is item active")


class CoreItemResponse(CoreItemBase):
    """Schema for CoreItem response"""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True