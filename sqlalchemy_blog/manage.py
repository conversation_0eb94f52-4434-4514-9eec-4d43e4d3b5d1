#!/usr/bin/env python3
"""
Sqlalchemy_Blog Management Script
"""

import os
import sys
from pathlib import Path

# Add project to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set default settings module
os.environ.setdefault("FABIPLUS_SETTINGS_MODULE", "sqlalchemy_blog.settings")

if __name__ == "__main__":
    from fabiplus.cli.main import main

    main()
