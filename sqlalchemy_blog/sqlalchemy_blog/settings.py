"""
Sqlalchemy_Blog Settings
FABI+ project configuration with SQLALCHEMY backend
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Project specific settings
APP_NAME = "Sqlalchemy_Blog API"
DEBUG = True

# ORM Backend Configuration
ORM_BACKEND = "sqlalchemy"

# Authentication Backend Configuration
AUTH_BACKEND = "oauth2"

# JWT Configuration (if using JWT backend)
JWT_SECRET_KEY = env("JWT_SECRET_KEY", "your-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 30
JWT_REFRESH_TOKEN_EXPIRE_DAYS = 7

# OAuth2 Configuration (default)
OAUTH2_TOKEN_URL = "/auth/token"
OAUTH2_SCOPES = {"read": "Read access", "write": "Write access", "admin": "Admin access"}

# Admin Routes Visibility
ADMIN_ROUTES_IN_DOCS = false

"""
Database settings for SQLAlchemy
Generated by FABI+ framework
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./sqlalchemy_blog.db")

# SQLAlchemy specific settings
SQLALCHEMY_DATABASE_URL = DATABASE_URL
SQLALCHEMY_ENGINE_OPTIONS = {
    "echo": DEBUG,
    "pool_pre_ping": True,
    "pool_recycle": 3600,
}

# Migration settings
ALEMBIC_CONFIG = {
    "script_location": "migrations",
    "file_template": "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s",
    "timezone": "UTC",
}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]


# Installed Apps
INSTALLED_APPS = [
    "apps.core",
    # Add your apps here:
    # "apps.blog",
    # "apps.users",
]

# API Configuration
API_PREFIX = "/api/v1"

# Admin Configuration
ADMIN_ENABLED = True
ADMIN_PREFIX = "/admin"

# Security
SECRET_KEY = "sqlalchemy_blog-dev-secret-key-change-in-production"

# CORS
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]