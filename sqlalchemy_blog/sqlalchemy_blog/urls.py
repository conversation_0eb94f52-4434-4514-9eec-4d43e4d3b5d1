"""
Sqlalchemy_Blog URL Configuration
"""

from fastapi import APIRouter
from fabiplus.api.auto import get_api_router
from fabiplus.admin.routes import admin_router

# Main router
router = APIRouter()

# Include auto-generated API routes
api_router = get_api_router()
router.include_router(api_router)

# Include admin routes
router.include_router(admin_router)

# Custom routes can be added here
@router.get("/")
async def root():
    return {
        "message": "Welcome to Sqlalchemy_Blog API",
        "version": "1.0.0",
        "docs": "/docs",
        "admin": "/admin"
    }