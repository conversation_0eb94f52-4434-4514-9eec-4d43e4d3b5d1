[tool:pytest]
testpaths = fabiplus/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    cli: marks tests as CLI tests
    template: marks tests as template tests
    orm: marks tests as ORM tests
    auth: marks tests as authentication tests
    admin: marks tests as admin interface tests
    api: marks tests as API tests
    cache: marks tests as caching tests
    security: marks tests as security tests
    performance: marks tests as performance tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:alembic.*
