# Pre-commit configuration for FABI+ framework
# Install with: pre-commit install
# Run manually: pre-commit run --all-files

repos:
  # Pre-commit hooks for basic checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: ^.*\.(md|rst)$
      - id: end-of-file-fixer
        exclude: ^.*\.(md|rst)$
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable

  # Black code formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]
        exclude: ^(migrations/|venv/|build/|dist/)

  # isort import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        exclude: ^(migrations/|venv/|build/|dist/)

  # flake8 linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-bugbear
          - flake8-docstrings
        exclude: ^(migrations/|venv/|build/|dist/)

  # mypy type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-redis
        exclude: ^(tests/|migrations/|venv/|build/|dist/)
        args: [--ignore-missing-imports]

  # Bandit security linting
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, -f, json, -o, bandit-report.json]
        exclude: ^(tests/|venv/|build/|dist/)

  # Safety dependency vulnerability check
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: pyproject.toml

  # Prettier for markdown and other files
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [markdown, yaml, json]
        exclude: ^(poetry.lock|.*\.min\.(js|css))$

  # Check for secrets
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: ^(poetry.lock|.*\.lock)$

# Configuration for specific hooks
default_language_version:
  python: python3.10

# Exclude patterns that apply to all hooks
exclude: |
  (?x)^(
    migrations/.*|
    venv/.*|
    build/.*|
    dist/.*|
    \.git/.*|
    \.pytest_cache/.*|
    \.mypy_cache/.*|
    __pycache__/.*|
    .*\.egg-info/.*|
    \.coverage.*|
    htmlcov/.*
  )$

# Fail fast - stop on first failure
fail_fast: false

# Minimum pre-commit version
minimum_pre_commit_version: 3.0.0
