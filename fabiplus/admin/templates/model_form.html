{% extends "base.html" %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="/admin/">Dashboard</a>
</li>
<li class="breadcrumb-item">
    <a href="/admin/{{ model_name }}/">{{ model_info.verbose_name_plural }}</a>
</li>
<li class="breadcrumb-item active">
    {% if action == 'add' %}
        Add {{ model_info.verbose_name }}
    {% else %}
        Change {{ model_info.verbose_name }}
    {% endif %}
</li>
{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/admin/{{ model_name }}/" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i>
        Back to List
    </a>
    {% if action == 'change' and object %}
    <button type="button" class="btn btn-outline-danger"
            hx-delete="/admin/{{ model_name }}/{{ object.id }}/"
            hx-confirm="Are you sure you want to delete this {{ model_info.verbose_name|lower }}?"
            hx-target="body">
        <i class="bi bi-trash me-1"></i>
        Delete
    </button>
    {% endif %}
</div>
{% endblock %}

{% block content %}
<!-- Success/Error Messages -->
<div id="message-container">
    {% if error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <strong>Error:</strong> {{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endif %}
</div>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="bi bi-{% if action == 'add' %}plus{% else %}pencil{% endif %} me-2"></i>
            {% if action == 'add' %}
                Add {{ model_info.verbose_name }}
            {% else %}
                Change {{ model_info.verbose_name }}
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if action == 'add' %}
                Create a new {{ model_info.verbose_name|lower }}
            {% else %}
                Modify the {{ model_info.verbose_name|lower }} details
            {% endif %}
        </p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-form me-2"></i>
                    {{ model_info.verbose_name }} Details
                </h5>
            </div>
            <div class="card-body">
                <form method="post"
                      hx-post="{% if action == 'add' %}/admin/{{ model_name }}/add/{% else %}/admin/{{ model_name }}/{{ object.id }}/{% endif %}"
                      hx-target="body"
                      hx-indicator="#form-loading">
                    
                    <div id="form-loading" class="htmx-indicator">
                        <div class="d-flex justify-content-center">
                            <div class="loading-spinner me-2"></div>
                            <span>Saving...</span>
                        </div>
                    </div>
                    
                    {% for field_info in model_info.fields %}
                    {% if field_info.name not in ['id', 'created_at', 'updated_at'] %}
                    <div class="mb-3">
                        <label for="{{ field_info.name }}" class="form-label">
                            {{ field_info.verbose_name or field_info.name|title }}
                            {% if field_info.required %}
                            <span class="text-danger">*</span>
                            {% endif %}
                        </label>

                        {% set field_value = object[field_info.name] if object else field_info.default %}

                        <!-- Dynamic field rendering based on widget_type -->
                        {% if field_info.widget_type == 'checkbox' %}
                            <!-- Boolean Field -->
                            <div class="form-check">
                                <input type="checkbox"
                                       class="form-check-input"
                                       id="{{ field_info.name }}"
                                       name="{{ field_info.name }}"
                                       value="true"
                                       {% if field_value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ field_info.name }}">
                                    {% if field_info.widget_attrs and field_info.widget_attrs.label %}
                                        {{ field_info.widget_attrs.label }}
                                    {% else %}
                                        {{ field_info.verbose_name }}
                                    {% endif %}
                                </label>
                            </div>
                            <!-- Hidden input to ensure false value is sent when unchecked -->
                            <input type="hidden" name="{{ field_info.name }}_unchecked" value="false">





                        {% elif field_info.widget_type == 'select' or field_info.is_foreign_key %}
                            <!-- Foreign Key / Relationship Field -->
                            <select class="form-select"
                                    id="{{ field_info.name }}"
                                    name="{{ field_info.name }}"
                                    {% if field_info.required %}required{% endif %}
                                    data-field-type="foreign-key"
                                    data-current-value="{{ field_value or '' }}"
                                    {% if field_info.related_model %}data-related-model="{{ field_info.related_model }}"{% endif %}>
                                <option value="">-- Select {{ field_info.verbose_name }} --</option>
                                <!-- Options will be populated via JavaScript -->
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="bi bi-link-45deg"></i>
                                    Related {{ field_info.verbose_name }}
                                </small>
                            </div>

                        {% elif field_info.widget_type == 'textarea' %}
                            <!-- Text Area Field -->
                            <textarea class="form-control"
                                      id="{{ field_info.name }}"
                                      name="{{ field_info.name }}"
                                      {% for attr, value in field_info.widget_attrs.items() %}{{ attr }}="{{ value }}" {% endfor %}
                                      {% if field_info.required %}required{% endif %}>{{ field_value or '' }}</textarea>

                        {% elif field_info.widget_type == 'number' %}
                            <!-- Numeric Field -->
                            <input class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{{ field_value or '' }}"
                                   {% for attr, value in field_info.widget_attrs.items() %}{{ attr }}="{{ value }}" {% endfor %}
                                   {% if field_info.required %}required{% endif %}>

                        {% elif field_info.widget_type == 'datetime-local' %}
                            <!-- DateTime Field -->
                            <input type="datetime-local"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{% if field_value %}{{ field_value.strftime('%Y-%m-%dT%H:%M') if field_value.strftime else field_value }}{% endif %}"
                                   {% if field_info.required %}required{% endif %}>

                        {% elif field_info.widget_type == 'date' %}
                            <!-- Date Field -->
                            <input type="date"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{% if field_value %}{{ field_value.strftime('%Y-%m-%d') if field_value.strftime else field_value }}{% endif %}"
                                   {% if field_info.required %}required{% endif %}>

                        {% elif field_info.widget_type == 'email' %}
                            <!-- Email Field -->
                            <input type="email"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{{ field_value or '' }}"
                                   {% if field_info.required %}required{% endif %}>

                        {% elif field_info.widget_type == 'tel' %}
                            <!-- Phone Field -->
                            <input type="tel"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{{ field_value or '' }}"
                                   {% if field_info.required %}required{% endif %}
                                   placeholder="+****************">

                        {% elif field_info.widget_type == 'url' %}
                            <!-- URL Field -->
                            <input type="url"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{{ field_value or '' }}"
                                   {% if field_info.required %}required{% endif %}
                                   placeholder="https://example.com">

                        {% elif field_info.widget_type == 'password' %}
                            <!-- Password Field -->
                            <input type="password"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   {% if action == 'add' and field_info.required %}required{% endif %}
                                   placeholder="{% if action == 'change' %}Leave blank to keep current password{% endif %}">
                            {% if action == 'change' %}
                            <div class="form-text">Leave blank to keep the current password.</div>
                            {% endif %}

                        {% else %}
                            <!-- Default text input for unknown widget types -->
                            <input type="text"
                                   class="form-control"
                                   id="{{ field_info.name }}"
                                   name="{{ field_info.name }}"
                                   value="{{ field_value or '' }}"
                                   {% if field_info.required %}required{% endif %}>
                        {% endif %}
                        
                        {% if field_info.description %}
                        <div class="form-text">{{ field_info.description }}</div>
                        {% endif %}
                    </div>
                    {% endif %}
                    {% endfor %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="/admin/{{ model_name }}/" class="btn btn-secondary">
                            <i class="bi bi-x me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check me-1"></i>
                            {% if action == 'add' %}Create{% else %}Update{% endif %} {{ model_info.verbose_name }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Sidebar with additional info -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Information
                </h6>
            </div>
            <div class="card-body">
                {% if action == 'change' and object %}
                <div class="mb-3">
                    <strong>ID:</strong><br>
                    <code>{{ object.id }}</code>
                </div>
                
                {% if object.created_at %}
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ object.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                </div>
                {% endif %}
                
                {% if object.updated_at %}
                <div class="mb-3">
                    <strong>Last Modified:</strong><br>
                    <small class="text-muted">{{ object.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                </div>
                {% endif %}
                {% endif %}
                
                <div class="mb-3">
                    <strong>Model:</strong><br>
                    <span class="badge bg-primary">{{ model_info.name }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Fields:</strong><br>
                    <small class="text-muted">{{ model_info.fields|length }} total fields</small>
                </div>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-question-circle me-2"></i>
                    Help
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-2">
                    <strong>Required fields</strong> are marked with a red asterisk (*).
                </p>
                <p class="small text-muted mb-2">
                    Use <kbd>Ctrl+S</kbd> to save the form quickly.
                </p>
                <p class="small text-muted mb-0">
                    Changes are saved immediately when you submit the form.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Keyboard shortcut for saving
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            document.querySelector('form').dispatchEvent(new Event('submit'));
        }
    });

    // Auto-resize textareas
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Load foreign key options
    document.querySelectorAll('select[data-field-type="foreign-key"]').forEach(select => {
        const fieldName = select.name;
        const relatedModel = select.dataset.relatedModel;

        if (relatedModel) {
            // Load options for foreign key fields
            fetch(`/admin/${relatedModel}/field-choices/${fieldName}/`)
                .then(response => response.json())
                .then(data => {
                    if (data.choices) {
                        data.choices.forEach(choice => {
                            const option = document.createElement('option');
                            option.value = choice.value;
                            option.textContent = choice.label;

                            // Check if this option should be selected
                            const currentValue = select.dataset.currentValue || '{{ field_value }}';
                            if (choice.value === currentValue) {
                                option.selected = true;
                            }

                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading foreign key options:', error);
                });
        }
    });

    // Form validation feedback
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });

    // Enhanced form field interactions

    // Number field validation
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const min = parseFloat(this.min);
            const value = parseFloat(this.value);

            if (!isNaN(min) && !isNaN(value) && value < min) {
                this.setCustomValidity(`Value must be at least ${min}`);
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // Email field validation
    document.querySelectorAll('input[type="email"]').forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.checkValidity()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
    });

    // URL field validation
    document.querySelectorAll('input[type="url"]').forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.checkValidity()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>
{% endblock %}
