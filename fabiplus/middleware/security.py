"""
FABI+ Framework Security Middleware
Provides CSRF protection, XSS protection, and other security features
"""

import html
import re
import secrets
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON><PERSON>esponse

from ..conf.settings import settings


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Security middleware for FABI+ framework
    Provides CSRF protection, XSS protection, and security headers
    """

    def __init__(self, app, csrf_enabled: bool = True, xss_protection: bool = True):
        super().__init__(app)
        self.csrf_enabled = csrf_enabled
        self.xss_protection = xss_protection
        self.csrf_tokens = {}  # In production, use Redis or database

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through security middleware"""
        import logging

        logger = logging.getLogger(__name__)

        # CSRF protection for state-changing methods (check BEFORE processing request)
        if self.csrf_enabled and request.method in ["POST", "PUT", "PATCH", "DELETE"]:
            logger.info(
                f"SecurityMiddleware: Checking CSRF for {request.method} {request.url.path}"
            )

            # Skip CSRF validation for authentication endpoints
            should_skip = self._should_skip_csrf(request)
            logger.info(
                f"SecurityMiddleware: Should skip CSRF for {request.url.path}: {should_skip}"
            )

            if not should_skip:
                csrf_valid = await self._validate_csrf_token(request)
                logger.info(
                    f"SecurityMiddleware: CSRF validation result for {request.url.path}: {csrf_valid}"
                )

                if not csrf_valid:
                    logger.warning(
                        f"SecurityMiddleware: CSRF validation failed for {request.url.path}"
                    )
                    return JSONResponse(
                        status_code=403,
                        content={"detail": "CSRF token validation failed"},
                    )
            else:
                logger.info(
                    f"SecurityMiddleware: Skipping CSRF validation for {request.url.path}"
                )

        # Process the request
        logger.info(
            f"SecurityMiddleware: Processing request {request.method} {request.url.path}"
        )
        response = await call_next(request)
        logger.info(
            f"SecurityMiddleware: Request processed, status: {response.status_code}"
        )

        # Add security headers
        self._add_security_headers(response, request)

        # Skip XSS protection for now to avoid content-length issues
        # TODO: Implement proper XSS protection without interfering with response streaming

        return response

    def _add_security_headers(self, response: Response, request: Request = None):
        """Add security headers to response"""

        # Prevent clickjacking
        response.headers["X-Frame-Options"] = "DENY"

        # Prevent MIME type sniffing
        response.headers["X-Content-Type-Options"] = "nosniff"

        # XSS protection
        response.headers["X-XSS-Protection"] = "1; mode=block"

        # Referrer policy
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # Content Security Policy - Allow CDN resources for docs and admin
        if request and (
            request.url.path in ["/docs", "/redoc"]
            or request.url.path.startswith("/admin/")
        ):
            # Relaxed CSP for documentation and admin pages
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://cdn.jsdelivr.net https://unpkg.com; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com https://fonts.googleapis.com; "
                "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; "
                "img-src 'self' data: https:; "
                "connect-src 'self'; "
                "worker-src 'self' blob:"
            )
        else:
            # Strict CSP for other pages
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self'"
            )

        # HSTS (only in production)
        if settings.ENVIRONMENT == "production":
            response.headers[
                "Strict-Transport-Security"
            ] = "max-age=31536000; includeSubDomains; preload"

    async def _validate_csrf_token(self, request: Request) -> bool:
        """Validate CSRF token"""

        # Get CSRF token from header first
        csrf_token = request.headers.get("X-CSRF-Token")

        if not csrf_token:
            # For now, skip CSRF validation if no header token is provided
            # This avoids the issue of consuming the request body
            # TODO: Implement proper CSRF token handling that doesn't consume the request body
            return True

        # Validate token (simplified implementation)
        # In production, implement proper token validation with expiration
        return len(csrf_token) >= 32

    def _should_skip_csrf(self, request: Request) -> bool:
        """Check if CSRF validation should be skipped for this request"""

        # Skip CSRF for authentication endpoints
        auth_endpoints = [
            "/auth/token",
            "/auth/login",
            "/admin/login/",
            "/admin/api/login/",
        ]

        # Check if request path matches any auth endpoint
        for endpoint in auth_endpoints:
            if request.url.path == endpoint or request.url.path.startswith(endpoint):
                return True

        # Skip CSRF for API endpoints with Bearer token
        auth_header = request.headers.get("Authorization", "")
        if auth_header.startswith("Bearer "):
            return True

        # Skip CSRF for requests with content-type application/json (API requests)
        content_type = request.headers.get("content-type", "")
        if content_type.startswith("application/json"):
            return True

        return False

    async def _apply_xss_protection(
        self, request: Request, response: Response
    ) -> Response:
        """Apply XSS protection to response"""

        # Only process JSON responses
        content_type = response.headers.get("content-type", "")
        if not content_type.startswith("application/json"):
            return response

        # Get response body
        body = b""
        async for chunk in response.body_iterator:
            body += chunk

        # Sanitize JSON content (basic implementation)
        try:
            import json

            data = json.loads(body.decode())
            sanitized_data = self._sanitize_data(data)
            sanitized_body = json.dumps(sanitized_data).encode()

            # Create new response with sanitized content
            return Response(
                content=sanitized_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type="application/json",
            )
        except (json.JSONDecodeError, UnicodeDecodeError):
            # Return original response if can't process
            return response

    def _sanitize_data(self, data):
        """Recursively sanitize data for XSS protection"""

        if isinstance(data, dict):
            return {key: self._sanitize_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        elif isinstance(data, str):
            return self._sanitize_string(data)
        else:
            return data

    def _sanitize_string(self, text: str) -> str:
        """Sanitize string for XSS protection"""

        # HTML escape
        text = html.escape(text)

        # Remove potentially dangerous patterns
        dangerous_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
        ]

        for pattern in dangerous_patterns:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE | re.DOTALL)

        return text

    def generate_csrf_token(self) -> str:
        """Generate CSRF token"""
        token = secrets.token_urlsafe(32)
        self.csrf_tokens[token] = time.time()
        return token

    def cleanup_expired_tokens(self):
        """Clean up expired CSRF tokens"""
        current_time = time.time()
        expired_tokens = [
            token
            for token, timestamp in self.csrf_tokens.items()
            if current_time - timestamp > 3600  # 1 hour expiration
        ]

        for token in expired_tokens:
            del self.csrf_tokens[token]


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware
    """

    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests = {}  # In production, use Redis

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting"""

        client_ip = self._get_client_ip(request)
        current_time = time.time()

        # Clean old requests
        self._cleanup_old_requests(current_time)

        # Check rate limit
        if client_ip in self.requests:
            request_times = self.requests[client_ip]
            recent_requests = [
                req_time
                for req_time in request_times
                if current_time - req_time < 60  # Last minute
            ]

            if len(recent_requests) >= self.requests_per_minute:
                return JSONResponse(
                    status_code=429, content={"detail": "Rate limit exceeded"}
                )

            self.requests[client_ip] = recent_requests + [current_time]
        else:
            self.requests[client_ip] = [current_time]

        return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _cleanup_old_requests(self, current_time: float):
        """Clean up old request records"""
        for client_ip in list(self.requests.keys()):
            self.requests[client_ip] = [
                req_time
                for req_time in self.requests[client_ip]
                if current_time - req_time < 60
            ]

            if not self.requests[client_ip]:
                del self.requests[client_ip]
