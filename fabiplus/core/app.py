"""
FABI+ Framework Application Factory
Creates and configures the FastAPI application
"""

from fastapi import FastAP<PERSON>, HTTPException, status, Depends, Form, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import logging
from pathlib import Path

from .auth import auth_backend, AuthenticationError, PermissionError
from .apps import apps
from .docs import setup_docs_customization

from ..conf.settings import settings
from ..middleware.security import SecurityMiddleware
from ..middleware.logging import LoggingMiddleware


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)

# Silence bcrypt warnings
logging.getLogger('passlib.handlers.bcrypt').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting FABI+ application...")

    # Reload settings to pick up any project-specific configuration
    from ..conf.settings import reload_settings
    settings = reload_settings()

    # Load installed apps
    try:
        apps.populate(settings.INSTALLED_APPS)
        logger.info(f"Loaded {len(settings.INSTALLED_APPS)} apps")
    except Exception as e:
        logger.error(f"Error loading apps: {e}")

    # Discover models from all loaded apps
    try:
        from ..core.models import ModelRegistry
        ModelRegistry.discover_models()
        models = ModelRegistry.get_all_models()
        logger.info(f"Discovered {len(models)} models: {list(models.keys())}")
    except Exception as e:
        logger.error(f"Error discovering models: {e}")

    # Generate and include API routes after models are loaded
    try:
        from ..api.auto import get_api_router
        api_router = get_api_router()
        app.include_router(api_router)
        logger.info("API routes generated and included")
    except Exception as e:
        logger.error(f"Error generating API routes: {e}")

    # Discover and include custom app routers
    try:
        custom_routers = _discover_app_routers()
        for app_name, router in custom_routers.items():
            app.include_router(router)
            logger.info(f"Custom router from '{app_name}' included")
        if custom_routers:
            logger.info(f"Included {len(custom_routers)} custom app routers")
    except Exception as e:
        logger.error(f"Error discovering custom routers: {e}")

    # Note: Database tables are created via migrations, not on startup

    yield

    # Shutdown
    logger.info("Shutting down FABI+ application...")


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application
    """
    
    # Create FastAPI app
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description=f"{settings.APP_NAME} - Built with FABI+ Framework",
        debug=settings.DEBUG,
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_CREDENTIALS,
        allow_methods=settings.CORS_METHODS,
        allow_headers=settings.CORS_HEADERS,
    )
    
    # Add custom middleware
    app.add_middleware(SecurityMiddleware)
    app.add_middleware(LoggingMiddleware)

    # Add activity logging middleware
    try:
        from ..middleware.activity import ActivityLoggingMiddleware
        app.add_middleware(
            ActivityLoggingMiddleware,
            log_all_requests=settings.DEBUG,  # Log all requests in debug mode
            exclude_paths=[
                "/docs", "/redoc", "/openapi.json", "/favicon.ico",
                "/static/", "/admin/static/", "/health", "/metrics",
                "/admin/logs/live"  # Exclude WebSocket endpoint
            ]
        )
        logger.info("Activity logging middleware enabled")
    except ImportError as e:
        logger.warning(f"Activity logging middleware not available: {e}")
    
    # Exception handlers
    @app.exception_handler(AuthenticationError)
    async def auth_exception_handler(_request, exc: AuthenticationError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=exc.headers
        )

    @app.exception_handler(PermissionError)
    async def permission_exception_handler(_request, exc: PermissionError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )

    @app.exception_handler(HTTPException)
    async def http_exception_handler(_request, exc: HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(_request, exc: Exception):
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with API information"""
        return {
            "message": f"Welcome to {settings.APP_NAME}",
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "api_docs": "/docs",
            "admin_panel": settings.ADMIN_PREFIX if settings.ADMIN_ENABLED else None,
            "api_prefix": settings.API_PREFIX
        }
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT
        }
    
    # Authentication endpoints
    @app.post("/auth/token", tags=["Authentication"])
    async def login(request: Request):
        """
        OAuth2 compatible token login endpoint
        Accepts form data (application/x-www-form-urlencoded)
        """
        try:
            logger.info("OAuth2 token endpoint reached")

            # Get form data manually with timeout and error handling
            logger.info("About to parse OAuth2 form data...")

            # Check content type first
            content_type = request.headers.get("content-type", "")
            logger.info(f"OAuth2 request content-type: {content_type}")

            if not content_type.startswith("application/x-www-form-urlencoded"):
                logger.error(f"Invalid content-type for OAuth2 form data: {content_type}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid content-type. Expected application/x-www-form-urlencoded",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Parse form data manually from raw body
            try:
                body = await request.body()
                logger.info(f"OAuth2 raw body received, length: {len(body)}")

                # Parse URL-encoded form data manually
                from urllib.parse import parse_qs
                body_str = body.decode('utf-8')
                logger.info(f"OAuth2 body string: {body_str[:100]}...")  # Log first 100 chars

                parsed_data = parse_qs(body_str)
                logger.info(f"OAuth2 parsed form data keys: {list(parsed_data.keys())}")

                # Extract username and password (parse_qs returns lists)
                username = parsed_data.get('username', [None])[0]
                password = parsed_data.get('password', [None])[0]

                logger.info(f"OAuth2 extracted username: {username}, password present: {bool(password)}")

            except Exception as e:
                logger.error(f"OAuth2 manual form parsing failed: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to parse form data",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            logger.info(f"OAuth2 token authentication attempt for user: {username}")
            logger.info(f"Form data received - username: {username}, password length: {len(password) if password else 0}")

            if not username or not password:
                logger.error("Missing username or password in OAuth2 form data")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username and password are required",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            user = auth_backend.authenticate_user(username, password)
            if not user:
                logger.warning(f"OAuth2 authentication failed for user: {username}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect username or password",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            logger.info(f"OAuth2 authentication successful for user: {username}")
            access_token = auth_backend.create_access_token(
                data={"sub": str(user.id), "username": user.username}
            )

            logger.info(f"Returning OAuth2 token for user: {username}")
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_staff": user.is_staff,
                    "is_superuser": user.is_superuser
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"OAuth2 authentication error for {username}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error"
            )
    
    from pydantic import BaseModel

    class LoginRequest(BaseModel):
        username: str
        password: str

    @app.post("/auth/login", tags=["Authentication"])
    async def login_json(credentials: LoginRequest):
        """
        JSON login endpoint for better API integration
        """
        try:
            logger.info(f"JSON authentication attempt for user: {credentials.username}")
            user = auth_backend.authenticate_user(credentials.username, credentials.password)
            if not user:
                logger.warning(f"JSON authentication failed for user: {credentials.username}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect username or password"
                )

            logger.info(f"JSON authentication successful for user: {credentials.username}")
            access_token = auth_backend.create_access_token(
                data={"sub": str(user.id), "username": user.username}
            )

            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_staff": user.is_staff,
                    "is_superuser": user.is_superuser
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"JSON authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error"
            )
    
    # API router is now included in lifespan startup after models are loaded
    
    # Include admin router if enabled
    if settings.ADMIN_ENABLED:
        from ..admin.routes import main_admin_router
        app.include_router(main_admin_router)

        # Mount admin static files if UI is enabled
        if settings.ADMIN_UI_ENABLED:
            admin_static_path = Path(__file__).parent.parent / "admin" / "static"
            if admin_static_path.exists():
                app.mount(
                    f"{settings.ADMIN_PREFIX}/static",
                    StaticFiles(directory=str(admin_static_path)),
                    name="admin-static"
                )

    # Setup custom documentation
    try:
        setup_docs_customization(app, settings)
    except Exception as e:
        logger.warning(f"Could not setup custom documentation: {e}")

    # Load and register plugins
    _load_plugins(app)
    
    logger.info(f"FABI+ application created successfully")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    logger.info(f"API prefix: {settings.API_PREFIX}")
    logger.info(f"Admin enabled: {settings.ADMIN_ENABLED}")
    
    return app


def _discover_app_routers():
    """Discover and load custom routers from installed apps"""
    import importlib
    from fastapi import APIRouter
    from typing import Dict

    custom_routers: Dict[str, APIRouter] = {}

    for app_name in settings.INSTALLED_APPS:
        try:
            # Try to import the app's views module
            views_module_path = f"{app_name}.views"
            views_module = importlib.import_module(views_module_path)

            # Look for a router in the views module
            if hasattr(views_module, 'router') and isinstance(views_module.router, APIRouter):
                custom_routers[app_name] = views_module.router
                logger.info(f"Found custom router in {app_name}.views")
                continue

        except ImportError:
            # No views module, try urls module
            pass

        try:
            # Try to import the app's urls module
            urls_module_path = f"{app_name}.urls"
            urls_module = importlib.import_module(urls_module_path)

            # Look for a router in the urls module
            if hasattr(urls_module, 'router') and isinstance(urls_module.router, APIRouter):
                custom_routers[app_name] = urls_module.router
                logger.info(f"Found custom router in {app_name}.urls")

        except ImportError:
            # No urls module either, skip this app
            logger.debug(f"No custom router found for app '{app_name}'")
            continue
        except Exception as e:
            logger.warning(f"Error loading router from app '{app_name}': {e}")
            continue

    return custom_routers


def _load_plugins(app: FastAPI):
    """Load and register plugins"""
    for plugin_path in settings.INSTALLED_PLUGINS:
        try:
            import importlib
            plugin_module = importlib.import_module(plugin_path)

            # Look for plugin registration function
            if hasattr(plugin_module, 'register_plugin'):
                plugin_module.register_plugin(app)
                logger.info(f"Plugin '{plugin_path}' loaded successfully")
            else:
                logger.warning(f"Plugin '{plugin_path}' has no register_plugin function")

        except ImportError as e:
            logger.error(f"Failed to load plugin '{plugin_path}': {e}")
        except Exception as e:
            logger.error(f"Error registering plugin '{plugin_path}': {e}")


# Create default app instance
app = create_app()
