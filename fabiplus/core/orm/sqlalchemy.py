"""
SQLAlchemy ORM backend for FABI+ framework
Pure SQLAlchemy implementation without SQLModel
"""

from pathlib import Path
from typing import Any, Dict, List, Tuple

from .base import BaseORMBackend, register_orm_backend


@register_orm_backend
class SQLAlchemyBackend(BaseORMBackend):
    """Pure SQLAlchemy ORM backend implementation"""

    @property
    def name(self) -> str:
        return "sqlalchemy"

    @property
    def dependencies(self) -> List[str]:
        return [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "sqlalchemy>=2.0.0",
            "pydantic>=2.5.0",
            "pydantic-settings>=2.1.0",
            "alembic>=1.13.0",
            "python-jose[cryptography]>=3.3.0",
            "passlib[bcrypt]>=1.7.4",
            "python-multipart>=0.0.6",
            "bcrypt>=4.1.0",
            "orjson>=3.9.0",
        ]

    @property
    def optional_dependencies(self) -> Dict[str, List[str]]:
        return {
            "postgresql": ["psycopg2-binary>=2.9.0"],
            "mysql": ["pymysql>=1.1.0"],
            "redis": ["redis>=5.0.0", "hiredis>=2.2.0"],
            "monitoring": ["sentry-sdk[fastapi]>=1.38.0"],
        }

    def generate_model_code(
        self, model_name: str, fields: List[Tuple[str, str]], app_name: str = None
    ) -> str:
        """Generate SQLAlchemy model code"""

        field_mapping = self.get_field_type_mapping()

        code = f'''"""
{model_name} model for {app_name or 'app'}
Generated by FABI+ framework with SQLAlchemy backend
"""

from typing import Optional
from sqlalchemy import Column, Integer, String, Boolean, Float, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from fabiplus.core.models import register_model

Base = declarative_base()


@register_model
class {model_name}(Base):
    """
    {model_name} model
    
    Generated with SQLAlchemy backend
    """
    __tablename__ = "{app_name}_{model_name.lower()}" if app_name else "{model_name.lower()}"
    
    id = Column(Integer, primary_key=True, index=True)
'''

        # Add fields
        for field_name, field_type in fields:
            sqlalchemy_type = field_mapping.get(field_type.lower(), "String(200)")

            if field_type.lower() in ["str", "string"]:
                code += f'    {field_name} = Column(String(200), nullable=False, comment="{field_name.title()}")\n'
            elif field_type.lower() == "text":
                code += f'    {field_name} = Column(Text, nullable=True, comment="{field_name.title()}")\n'
            elif field_type.lower() in ["int", "integer"]:
                code += f'    {field_name} = Column(Integer, nullable=False, comment="{field_name.title()}")\n'
            elif field_type.lower() in ["bool", "boolean"]:
                code += f'    {field_name} = Column(Boolean, default=False, comment="{field_name.title()}")\n'
            elif field_type.lower() in ["float", "decimal"]:
                code += f'    {field_name} = Column(Float, nullable=False, comment="{field_name.title()}")\n'
            elif field_type.lower() == "datetime":
                code += f'    {field_name} = Column(DateTime, nullable=True, comment="{field_name.title()}")\n'
            else:
                code += f'    {field_name} = Column(String(200), nullable=False, comment="{field_name.title()}")\n'

        # Add default fields if none provided
        if not fields:
            code += f'    name = Column(String(100), nullable=False, comment="{model_name} name")\n'
            code += f'    description = Column(Text, nullable=True, default="", comment="{model_name} description")\n'

        # Add model methods
        code += f"""
    def __repr__(self):
        return f"<{model_name}(id={{self.id}}, {fields[0][0] if fields else 'name'}={{self.{fields[0][0] if fields else 'name'}}})"
    
    def __str__(self):
        return str(self.{fields[0][0] if fields else 'name'})
    
    class Config:
        _verbose_name = "{model_name}"
        _verbose_name_plural = "{model_name}s"
"""

        return code

    def generate_settings_code(self) -> str:
        """Generate settings configuration for SQLAlchemy"""
        return f'''"""
Database settings for SQLAlchemy
Generated by FABI+ framework
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./{self.project_name}.db")

# SQLAlchemy specific settings
SQLALCHEMY_DATABASE_URL = DATABASE_URL
SQLALCHEMY_ENGINE_OPTIONS = {{
    "echo": DEBUG,
    "pool_pre_ping": True,
    "pool_recycle": 3600,
}}

# Migration settings
ALEMBIC_CONFIG = {{
    "script_location": "migrations",
    "file_template": "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s",
    "timezone": "UTC",
}}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]
'''

    def generate_database_init_code(self) -> str:
        """Generate database initialization code for SQLAlchemy"""
        return '''"""
Database initialization for SQLAlchemy
Generated by FABI+ framework
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from fabiplus.conf import settings

# Create engine
engine = create_engine(
    settings.DATABASE_URL,
    **settings.SQLALCHEMY_ENGINE_OPTIONS
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)

def get_engine():
    """Get database engine"""
    return engine

def get_session():
    """Get database session"""
    return SessionLocal()
'''

    def generate_migration_config(self, project_dir: Path) -> Dict[str, str]:
        """Generate Alembic migration configuration for SQLAlchemy"""

        alembic_ini = f"""# SQLAlchemy Alembic configuration

[alembic]
script_location = migrations
file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s
prepend_sys_path = .
timezone = UTC
truncate_slug_length = 40
revision_environment = false
sourceless = false
version_num_format = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s
version_path_separator = os
recursive_version_locations = false
output_encoding = utf-8

sqlalchemy.url = sqlite:///./{self.project_name}.db

[post_write_hooks]
hooks = black
black.type = console_scripts
black.entrypoint = black
black.options = -l 79 REVISION_SCRIPT_FILENAME

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %%(levelname)-5.5s [%%(name)s] %%(message)s
datefmt = %%H:%%M:%%S
"""

        env_py = '''"""
Alembic environment configuration for SQLAlchemy
Generated by FABI+ framework
"""

from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import FABI+ components
from fabiplus.core.models import ModelRegistry
from fabiplus.core.user_model import User  # Ensure User model is imported
from fabiplus.conf.settings import settings

# Ensure User model is registered
ModelRegistry.register(User)

# this is the Alembic Config object
config = context.config

# Interpret the config file for Python logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the SQLAlchemy URL
config.set_main_option("sqlalchemy.url", settings.DATABASE_URL)

# Discover all models to ensure they're loaded
ModelRegistry.discover_models()

# Get all registered models metadata
target_metadata = ModelRegistry.get_metadata()

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''

        return {
            "alembic.ini": alembic_ini,
            "migrations/env.py": env_py,
            "migrations/script.py.mako": '''"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
import fabiplus
import fabiplus.core.user_model
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
''',
        }

    def get_base_model_import(self) -> str:
        """Get the base model import statement for SQLAlchemy"""
        return "from fabiplus.core.models import BaseModel, register_model"

    def get_field_type_mapping(self) -> Dict[str, str]:
        """Get mapping of common field types to SQLAlchemy types"""
        return {
            "str": "String(200)",
            "string": "String(200)",
            "text": "Text",
            "int": "Integer",
            "integer": "Integer",
            "float": "Float",
            "decimal": "Float",
            "bool": "Boolean",
            "boolean": "Boolean",
            "datetime": "DateTime",
            "date": "Date",
            "time": "Time",
            "uuid": "String(36)",
            "json": "JSON",
        }

    def supports_async(self) -> bool:
        """SQLAlchemy 2.0+ supports async"""
        return True

    def get_admin_integration_code(self) -> str:
        """Generate admin interface integration code for SQLAlchemy"""
        return '''"""
Admin integration for SQLAlchemy
Generated by FABI+ framework
"""

from fabiplus.admin.base import AdminView
from sqlalchemy.orm import Session
from typing import Any, Dict, List

class SQLAlchemyAdminView(AdminView):
    """Base admin view for SQLAlchemy models"""
    
    def get_list(self, db: Session, skip: int = 0, limit: int = 100, 
                search: str = None, filters: Dict[str, Any] = None) -> List[Any]:
        """Get list of model instances"""
        query = db.query(self.model)
        
        # Apply search
        if search and hasattr(self.model, 'name'):
            query = query.filter(self.model.name.contains(search))
        
        # Apply filters
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.filter(getattr(self.model, field) == value)
        
        # Apply pagination
        return query.offset(skip).limit(limit).all()
    
    def get_detail(self, db: Session, id: Any) -> Any:
        """Get single model instance"""
        return db.query(self.model).filter(self.model.id == id).first()
    
    def create(self, db: Session, data: Dict[str, Any]) -> Any:
        """Create new model instance"""
        instance = self.model(**data)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance
    
    def update(self, db: Session, id: Any, data: Dict[str, Any]) -> Any:
        """Update model instance"""
        instance = db.query(self.model).filter(self.model.id == id).first()
        if instance:
            for field, value in data.items():
                if hasattr(instance, field):
                    setattr(instance, field, value)
            db.commit()
            db.refresh(instance)
        return instance
    
    def delete(self, db: Session, id: Any) -> bool:
        """Delete model instance"""
        instance = db.query(self.model).filter(self.model.id == id).first()
        if instance:
            db.delete(instance)
            db.commit()
            return True
        return False
'''
