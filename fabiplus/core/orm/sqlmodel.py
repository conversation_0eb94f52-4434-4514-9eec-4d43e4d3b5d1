"""
SQLModel ORM backend for FABI+ framework
Default ORM implementation using SQLModel + SQLAlchemy
"""

from pathlib import Path
from typing import Any, Dict, List, Tuple

from .base import BaseORMBackend, register_orm_backend


@register_orm_backend
class SQLModelBackend(BaseORMBackend):
    """SQLModel ORM backend implementation"""

    @property
    def name(self) -> str:
        return "sqlmodel"

    @property
    def dependencies(self) -> List[str]:
        return [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "sqlmodel>=0.0.14",
            "pydantic>=2.5.0",
            "pydantic-settings>=2.1.0",
            "sqlalchemy>=2.0.0",
            "alembic>=1.13.0",
            "python-jose[cryptography]>=3.3.0",
            "passlib[bcrypt]>=1.7.4",
            "python-multipart>=0.0.6",
            "bcrypt>=4.1.0",
            "orjson>=3.9.0",
        ]

    @property
    def optional_dependencies(self) -> Dict[str, List[str]]:
        return {
            "postgresql": ["psycopg2-binary>=2.9.0"],
            "mysql": ["pymysql>=1.1.0"],
            "redis": ["redis>=5.0.0", "hiredis>=2.2.0"],
            "monitoring": ["sentry-sdk[fastapi]>=1.38.0"],
        }

    def generate_model_code(
        self, model_name: str, fields: List[Tuple[str, str]], app_name: str = None
    ) -> str:
        """Generate SQLModel model code"""

        field_mapping = self.get_field_type_mapping()

        code = f'''"""
{model_name} model for {app_name or 'app'}
Generated by FABI+ framework
"""

from typing import Optional
from sqlmodel import SQLModel, Field
from fabiplus.core.models import BaseModel, register_model


@register_model
class {model_name}(BaseModel, table=True):
    """
    {model_name} model
    
    Generated with SQLModel backend
    """
    __tablename__ = "{app_name}_{model_name.lower()}" if app_name else "{model_name.lower()}"
    
'''

        # Add fields
        for field_name, field_type in fields:
            sqlmodel_type = field_mapping.get(field_type.lower(), "str")

            if field_type.lower() in ["str", "string", "text"]:
                code += f'    {field_name}: {sqlmodel_type} = Field(max_length=200, description="{field_name.title()}")\n'
            elif field_type.lower() in ["int", "integer"]:
                code += f'    {field_name}: {sqlmodel_type} = Field(description="{field_name.title()}")\n'
            elif field_type.lower() in ["bool", "boolean"]:
                code += f'    {field_name}: {sqlmodel_type} = Field(default=False, description="{field_name.title()}")\n'
            elif field_type.lower() in ["float", "decimal"]:
                code += f'    {field_name}: {sqlmodel_type} = Field(description="{field_name.title()}")\n'
            elif field_type.lower() == "datetime":
                code += f'    {field_name}: Optional[datetime] = Field(default=None, description="{field_name.title()}")\n'
            else:
                code += f'    {field_name}: {sqlmodel_type} = Field(description="{field_name.title()}")\n'

        # Add default fields if none provided
        if not fields:
            code += f'    name: str = Field(max_length=100, description="{model_name} name")\n'
            code += f'    description: Optional[str] = Field(default="", description="{model_name} description")\n'

        # Add model configuration
        code += f"""
    class Config:
        _verbose_name = "{model_name}"
        _verbose_name_plural = "{model_name}s"
        
    def __str__(self):
        return self.{fields[0][0] if fields else 'name'}
"""

        return code

    def generate_settings_code(self) -> str:
        """Generate settings configuration for SQLModel"""
        return f'''"""
Database settings for SQLModel
Generated by FABI+ framework
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./{self.project_name}.db")

# SQLModel specific settings
SQLMODEL_DATABASE_URL = DATABASE_URL
SQLALCHEMY_DATABASE_URL = DATABASE_URL

# Migration settings
ALEMBIC_CONFIG = {{
    "script_location": "migrations",
    "file_template": "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s",
    "timezone": "UTC",
}}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]
'''

    def generate_database_init_code(self) -> str:
        """Generate database initialization code for SQLModel"""
        return '''"""
Database initialization for SQLModel
Generated by FABI+ framework
"""

from sqlalchemy import create_engine
from sqlmodel import SQLModel
from fabiplus.conf import settings

# Create engine
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
)

def create_tables():
    """Create all database tables"""
    SQLModel.metadata.create_all(engine)

def get_engine():
    """Get database engine"""
    return engine
'''

    def generate_migration_config(self, project_dir: Path) -> Dict[str, str]:
        """Generate Alembic migration configuration"""

        alembic_ini = f"""# A generic, single database configuration.

[alembic]
# path to migration scripts
script_location = migrations

# template used to generate migration file names; The default value is %%(rev)s_%%(slug)s
file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s

# sys.path path, will be prepended to sys.path if present.
prepend_sys_path = .

# timezone to use when rendering the date within the migration file
# as well as the filename.
timezone = UTC

# max length of characters to apply to the
# "slug" field
truncate_slug_length = 40

# set to 'true' to run the environment during
# the 'revision' command, regardless of autogenerate
revision_environment = false

# set to 'true' to allow .pyc and .pyo files without
# a source .py file to be detected as revisions in the
# versions/ directory
sourceless = false

# version number format
version_num_format = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s

# version path separator; As mentioned above, this is the character used to split
# version_locations. The default within new alembic.ini files is "os", which uses
# os.pathsep. If this key is omitted entirely, it falls back to the legacy
# behavior of splitting on spaces and/or commas.
version_path_separator = os

# set to 'true' to search source files recursively
# in each "version_locations" directory
recursive_version_locations = false

# the output encoding used when revision files
# are written from script.py.mako
output_encoding = utf-8

sqlalchemy.url = sqlite:///./{self.project_name}.db

[post_write_hooks]
# post_write_hooks defines scripts or Python functions that are run
# on newly generated revision scripts.

# format using "black" - use the console_scripts runner, against the "black" entrypoint
hooks = black
black.type = console_scripts
black.entrypoint = black
black.options = -l 79 REVISION_SCRIPT_FILENAME

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %%(levelname)-5.5s [%%(name)s] %%(message)s
datefmt = %%H:%%M:%%S
"""

        env_py = '''"""
Alembic environment configuration for SQLModel
Generated by FABI+ framework
"""

import asyncio
from logging.config import fileConfig
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from alembic import context
from sqlmodel import SQLModel
import sqlmodel
import fabiplus
import fabiplus.core.user_model

# Import your models here
from fabiplus.core.models import *
from fabiplus.core.user_model import *

# this is the Alembic Config object
config = context.config

# Interpret the config file for Python logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here for 'autogenerate' support
target_metadata = SQLModel.metadata

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = context.config.attributes.get("connection", None)

    if connectable is None:
        connectable = create_engine(
            config.get_main_option("sqlalchemy.url"),
            poolclass=pool.NullPool,
        )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''

        return {
            "alembic.ini": alembic_ini,
            "migrations/env.py": env_py,
            "migrations/script.py.mako": '''"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel
import fabiplus.core.user_model
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
''',
        }

    def get_base_model_import(self) -> str:
        """Get the base model import statement for SQLModel"""
        return "from fabiplus.core.models import BaseModel, register_model"

    def get_field_type_mapping(self) -> Dict[str, str]:
        """Get mapping of common field types to SQLModel types"""
        return {
            "str": "str",
            "string": "str",
            "text": "str",
            "int": "int",
            "integer": "int",
            "float": "float",
            "decimal": "float",
            "bool": "bool",
            "boolean": "bool",
            "datetime": "datetime",
            "date": "date",
            "time": "time",
            "uuid": "UUID",
            "json": "dict",
        }

    def supports_async(self) -> bool:
        """SQLModel supports async through SQLAlchemy"""
        return True

    def get_admin_integration_code(self) -> str:
        """Generate admin interface integration code for SQLModel"""
        return '''"""
Admin integration for SQLModel
Generated by FABI+ framework
"""

from fabiplus.admin.base import AdminView
from sqlmodel import select
from typing import Any, Dict, List

class SQLModelAdminView(AdminView):
    """Base admin view for SQLModel models"""
    
    async def get_list(self, skip: int = 0, limit: int = 100, 
                      search: str = None, filters: Dict[str, Any] = None) -> List[Any]:
        """Get list of model instances"""
        from fabiplus.core.database import get_session
        
        async with get_session() as session:
            statement = select(self.model)
            
            # Apply search
            if search and hasattr(self.model, 'name'):
                statement = statement.where(self.model.name.contains(search))
            
            # Apply filters
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field):
                        statement = statement.where(getattr(self.model, field) == value)
            
            # Apply pagination
            statement = statement.offset(skip).limit(limit)
            
            result = await session.exec(statement)
            return result.all()
    
    async def get_detail(self, id: Any) -> Any:
        """Get single model instance"""
        from fabiplus.core.database import get_session
        
        async with get_session() as session:
            return await session.get(self.model, id)
    
    async def create(self, data: Dict[str, Any]) -> Any:
        """Create new model instance"""
        from fabiplus.core.database import get_session
        
        async with get_session() as session:
            instance = self.model(**data)
            session.add(instance)
            await session.commit()
            await session.refresh(instance)
            return instance
    
    async def update(self, id: Any, data: Dict[str, Any]) -> Any:
        """Update model instance"""
        from fabiplus.core.database import get_session
        
        async with get_session() as session:
            instance = await session.get(self.model, id)
            if instance:
                for field, value in data.items():
                    if hasattr(instance, field):
                        setattr(instance, field, value)
                await session.commit()
                await session.refresh(instance)
            return instance
    
    async def delete(self, id: Any) -> bool:
        """Delete model instance"""
        from fabiplus.core.database import get_session
        
        async with get_session() as session:
            instance = await session.get(self.model, id)
            if instance:
                await session.delete(instance)
                await session.commit()
                return True
            return False
'''
