"""
Tortoise ORM backend for FABI+ framework
Async-first ORM implementation
"""

from pathlib import Path
from typing import Any, Dict, List, Tuple

from .base import BaseORMBackend, register_orm_backend


@register_orm_backend
class TortoiseBackend(BaseORMBackend):
    """Tortoise ORM backend implementation"""

    @property
    def name(self) -> str:
        return "tortoise"

    @property
    def dependencies(self) -> List[str]:
        return [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "tortoise-orm>=0.20.0",
            "pydantic>=2.5.0",
            "pydantic-settings>=2.1.0",
            "aerich>=0.7.0",  # Migration tool for Tortoise
            "python-jose[cryptography]>=3.3.0",
            "passlib[bcrypt]>=1.7.4",
            "python-multipart>=0.0.6",
            "bcrypt>=4.1.0",
            "orjson>=3.9.0",
        ]

    @property
    def optional_dependencies(self) -> Dict[str, List[str]]:
        return {
            "postgresql": ["asyncpg>=0.29.0"],
            "mysql": ["aiomysql>=0.2.0"],
            "redis": ["redis>=5.0.0", "hiredis>=2.2.0"],
            "monitoring": ["sentry-sdk[fastapi]>=1.38.0"],
        }

    def generate_model_code(
        self, model_name: str, fields: List[Tuple[str, str]], app_name: str = None
    ) -> str:
        """Generate Tortoise ORM model code"""

        field_mapping = self.get_field_type_mapping()

        code = f'''"""
{model_name} model for {app_name or 'app'}
Generated by FABI+ framework with Tortoise ORM backend
"""

from typing import Optional
from tortoise.models import Model
from tortoise import fields
from fabiplus.core.models import register_model


@register_model
class {model_name}(Model):
    """
    {model_name} model
    
    Generated with Tortoise ORM backend
    """
    
    class Meta:
        table = "{app_name}_{model_name.lower()}" if app_name else "{model_name.lower()}"
        app = "{app_name}" if app_name else "default"
    
    id = fields.IntField(pk=True)
'''

        # Add fields
        for field_name, field_type in fields:
            tortoise_field = field_mapping.get(
                field_type.lower(), "CharField(max_length=200)"
            )

            if field_type.lower() in ["str", "string"]:
                code += f'    {field_name} = fields.CharField(max_length=200, description="{field_name.title()}")\n'
            elif field_type.lower() == "text":
                code += f'    {field_name} = fields.TextField(description="{field_name.title()}")\n'
            elif field_type.lower() in ["int", "integer"]:
                code += f'    {field_name} = fields.IntField(description="{field_name.title()}")\n'
            elif field_type.lower() in ["bool", "boolean"]:
                code += f'    {field_name} = fields.BooleanField(default=False, description="{field_name.title()}")\n'
            elif field_type.lower() in ["float", "decimal"]:
                code += f'    {field_name} = fields.FloatField(description="{field_name.title()}")\n'
            elif field_type.lower() == "datetime":
                code += f'    {field_name} = fields.DatetimeField(null=True, description="{field_name.title()}")\n'
            elif field_type.lower() == "date":
                code += f'    {field_name} = fields.DateField(null=True, description="{field_name.title()}")\n'
            elif field_type.lower() == "uuid":
                code += f'    {field_name} = fields.UUIDField(description="{field_name.title()}")\n'
            elif field_type.lower() == "json":
                code += f'    {field_name} = fields.JSONField(description="{field_name.title()}")\n'
            else:
                code += f'    {field_name} = fields.CharField(max_length=200, description="{field_name.title()}")\n'

        # Add default fields if none provided
        if not fields:
            code += f'    name = fields.CharField(max_length=100, description="{model_name} name")\n'
            code += f'    description = fields.TextField(default="", description="{model_name} description")\n'

        # Add timestamps
        code += """    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
"""

        # Add model methods
        code += f"""
    def __str__(self):
        return str(self.{fields[0][0] if fields else 'name'})
    
    class PydanticMeta:
        computed = ["id"]
        exclude_readonly = True
"""

        return code

    def generate_settings_code(self) -> str:
        """Generate settings configuration for Tortoise ORM"""
        return f"""
# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./{self.project_name}.db")

# Tortoise ORM Configuration
TORTOISE_ORM = {{
    "connections": {{
        "default": DATABASE_URL,
    }},
    "apps": {{
        "models": {{
            "models": ["apps.blog.models", "aerich.models"],
            "default_connection": "default",
        }},
    }},
    "use_tz": True,
    "timezone": "UTC",
}}

# Migration settings (Aerich)
AERICH_CONFIG = {{
    "tortoise_orm": TORTOISE_ORM,
    "location": "./migrations",
    "src_folder": "./",
}}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]
"""

    def generate_database_init_code(self) -> str:
        """Generate database initialization code for Tortoise ORM"""
        return '''"""
Database initialization for Tortoise ORM
Generated by FABI+ framework
"""

from tortoise import Tortoise
from fabiplus.conf import settings

async def init_db():
    """Initialize Tortoise ORM"""
    await Tortoise.init(config=settings.TORTOISE_ORM)

async def generate_schemas():
    """Generate database schemas"""
    await Tortoise.generate_schemas()

async def close_db():
    """Close database connections"""
    await Tortoise.close_connections()

def get_tortoise_config():
    """Get Tortoise ORM configuration"""
    return settings.TORTOISE_ORM
'''

    def generate_migration_config(self, project_dir: Path) -> Dict[str, str]:
        """Generate Aerich migration configuration for Tortoise ORM"""

        aerich_ini = f"""[tool.aerich]
tortoise_orm = "fabiplus.conf.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./"
"""

        pyproject_aerich = f"""# Add this to your pyproject.toml

[tool.aerich]
tortoise_orm = "{self.project_name}.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./"
"""

        return {
            "aerich.ini": aerich_ini,
            "pyproject_aerich_section.toml": pyproject_aerich,
        }

    def get_base_model_import(self) -> str:
        """Get the base model import statement for Tortoise ORM"""
        return "from fabiplus.core.models import BaseModel, register_model"

    def get_field_type_mapping(self) -> Dict[str, str]:
        """Get mapping of common field types to Tortoise ORM types"""
        return {
            "str": "CharField(max_length=200)",
            "string": "CharField(max_length=200)",
            "text": "TextField",
            "int": "IntField",
            "integer": "IntField",
            "float": "FloatField",
            "decimal": "DecimalField",
            "bool": "BooleanField",
            "boolean": "BooleanField",
            "datetime": "DatetimeField",
            "date": "DateField",
            "time": "TimeField",
            "uuid": "UUIDField",
            "json": "JSONField",
        }

    def supports_async(self) -> bool:
        """Tortoise ORM is async-first"""
        return True

    def get_admin_integration_code(self) -> str:
        """Generate admin interface integration code for Tortoise ORM"""
        return '''"""
Admin integration for Tortoise ORM
Generated by FABI+ framework
"""

from fabiplus.admin.base import AdminView
from typing import Any, Dict, List
from tortoise.queryset import QuerySet

class TortoiseAdminView(AdminView):
    """Base admin view for Tortoise ORM models"""
    
    async def get_list(self, skip: int = 0, limit: int = 100, 
                      search: str = None, filters: Dict[str, Any] = None) -> List[Any]:
        """Get list of model instances"""
        queryset: QuerySet = self.model.all()
        
        # Apply search
        if search and hasattr(self.model, 'name'):
            queryset = queryset.filter(name__icontains=search)
        
        # Apply filters
        if filters:
            queryset = queryset.filter(**filters)
        
        # Apply pagination
        return await queryset.offset(skip).limit(limit)
    
    async def get_detail(self, id: Any) -> Any:
        """Get single model instance"""
        return await self.model.get_or_none(id=id)
    
    async def create(self, data: Dict[str, Any]) -> Any:
        """Create new model instance"""
        return await self.model.create(**data)
    
    async def update(self, id: Any, data: Dict[str, Any]) -> Any:
        """Update model instance"""
        instance = await self.model.get_or_none(id=id)
        if instance:
            await instance.update_from_dict(data)
            await instance.save()
        return instance
    
    async def delete(self, id: Any) -> bool:
        """Delete model instance"""
        instance = await self.model.get_or_none(id=id)
        if instance:
            await instance.delete()
            return True
        return False
    
    async def get_count(self, search: str = None, filters: Dict[str, Any] = None) -> int:
        """Get total count of instances"""
        queryset: QuerySet = self.model.all()
        
        # Apply search
        if search and hasattr(self.model, 'name'):
            queryset = queryset.filter(name__icontains=search)
        
        # Apply filters
        if filters:
            queryset = queryset.filter(**filters)
        
        return await queryset.count()
'''
