#!/usr/bin/env python3
"""
Comprehensive FABI+ Framework Workflow Test
Tests complete workflow: project creation, migrations, users, permissions, API testing
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

class FABIPlusWorkflowTest:
    """Complete FABI+ workflow test"""
    
    def __init__(self, project_name="testproject_workflow", orm="sqlmodel"):
        self.project_name = project_name
        self.orm = orm
        self.base_dir = Path.cwd()
        self.project_dir = self.base_dir / project_name
        self.server_process = None
        self.base_url = "http://localhost:8000"
        self.api_url = f"{self.base_url}/api"
        
    def run_command(self, command, cwd=None, check=True):
        """Run a command and return the result"""
        if cwd is None:
            cwd = self.project_dir if self.project_dir.exists() else self.base_dir
            
        print(f"🔧 Running: {command}")
        print(f"📁 In directory: {cwd}")
        
        result = subprocess.run(
            command.split(),
            cwd=cwd,
            capture_output=True,
            text=True
        )
        
        if result.stdout:
            print(f"✅ Output: {result.stdout.strip()}")
        if result.stderr:
            print(f"⚠️  Error: {result.stderr.strip()}")
            
        if check and result.returncode != 0:
            print(f"❌ Command failed with return code {result.returncode}")
            raise subprocess.CalledProcessError(result.returncode, command)
            
        return result
    
    def step_1_create_project(self):
        """Step 1: Create FABI+ project"""
        print("\n" + "="*60)
        print("📦 STEP 1: Creating FABI+ Project")
        print("="*60)
        
        # Clean up existing project
        if self.project_dir.exists():
            print(f"🧹 Cleaning up existing project: {self.project_dir}")
            import shutil
            shutil.rmtree(self.project_dir)
        
        # Create new project
        self.run_command(f"fabiplus project startproject {self.project_name} --orm {self.orm}", cwd=self.base_dir)
        
        if not self.project_dir.exists():
            raise Exception(f"Project directory {self.project_dir} was not created")
            
        print(f"✅ Project '{self.project_name}' created successfully with {self.orm} ORM")
    
    def step_2_create_app(self):
        """Step 2: Create blog app"""
        print("\n" + "="*60)
        print("📱 STEP 2: Creating Blog App")
        print("="*60)
        
        # Create blog app
        process = subprocess.Popen(
            ["fabiplus", "app", "startapp", "blog"],
            cwd=self.project_dir,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send 'y' to confirm app creation
        stdout, stderr = process.communicate(input='y\n')
        
        if process.returncode != 0:
            print(f"❌ App creation failed: {stderr}")
            raise Exception("App creation failed")
            
        print("✅ Blog app created successfully")
        
        # Add models to the blog app
        self.add_blog_models()
    
    def add_blog_models(self):
        """Add models to blog app"""
        print("📝 Adding models to blog app...")
        
        models_file = self.project_dir / "apps" / "blog" / "models.py"
        
        if self.orm == "sqlmodel":
            models_content = '''@register_model
class Post(BaseModel, table=True):
    """Blog post model"""

    title: str = Field(max_length=200, description="Post title")
    content: str = Field(description="Post content")
    is_published: bool = Field(default=False, description="Is published")
    author: Optional[str] = Field(default=None, max_length=100, description="Author name")

    class Config:
        _verbose_name = "Blog Post"
        _verbose_name_plural = "Blog Posts"

    def __str__(self):
        return self.title


@register_model
class Category(BaseModel, table=True):
    """Blog category model"""

    name: str = Field(max_length=100, description="Category name")
    description: Optional[str] = Field(default="", description="Category description")
    is_active: bool = Field(default=True, description="Is active")

    class Config:
        _verbose_name = "Category"
        _verbose_name_plural = "Categories"

    def __str__(self):
        return self.name'''
        else:  # sqlalchemy
            models_content = '''@register_model
class Post(Base):
    """Blog post model"""
    __tablename__ = "blog_posts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, comment="Post title")
    content = Column(Text, nullable=False, comment="Post content")
    is_published = Column(Boolean, default=False, comment="Is published")
    author = Column(String(100), nullable=True, comment="Author name")

    def __str__(self):
        return self.title


@register_model
class Category(Base):
    """Blog category model"""
    __tablename__ = "blog_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="Category name")
    description = Column(Text, nullable=True, default="", comment="Category description")
    is_active = Column(Boolean, default=True, comment="Is active")

    def __str__(self):
        return self.name'''
        
        # Read current content and replace the example models
        with open(models_file, 'r') as f:
            content = f.read()
        
        # Find the example models section and replace it
        if "# Add your models here" in content:
            parts = content.split("# Add your models here")
            new_content = parts[0] + models_content
            
            with open(models_file, 'w') as f:
                f.write(new_content)
                
            print("✅ Models added to blog app")
        else:
            print("⚠️  Could not find models section to replace")
    
    def step_3_database_setup(self):
        """Step 3: Database migrations"""
        print("\n" + "="*60)
        print("🗄️  STEP 3: Database Setup")
        print("="*60)
        
        # Create migrations
        self.run_command('fabiplus db makemigrations --message "Initial_migration"')
        
        # Run migrations
        self.run_command("fabiplus db migrate")
        
        print("✅ Database setup completed")
    
    def step_4_create_users(self):
        """Step 4: Create users"""
        print("\n" + "="*60)
        print("👥 STEP 4: Creating Users")
        print("="*60)
        
        # Create superuser
        print("Creating superuser...")
        self.run_command("fabiplus user create --username admin --email <EMAIL> --password admin123 --superuser")
        
        # Create staff user
        print("Creating staff user...")
        self.run_command("fabiplus user create --username staff --email <EMAIL> --password staff123 --staff")
        
        # Create normal user
        print("Creating normal user...")
        self.run_command("fabiplus user create --username user --email <EMAIL> --password user123")
        
        print("✅ Users created successfully")
    
    def step_5_start_server(self):
        """Step 5: Start development server"""
        print("\n" + "="*60)
        print("🚀 STEP 5: Starting Development Server")
        print("="*60)
        
        # Start server in background
        self.server_process = subprocess.Popen(
            ["fabiplus", "server", "run", "--host", "0.0.0.0", "--port", "8000"],
            cwd=self.project_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        for i in range(30):
            try:
                response = requests.get(f"{self.base_url}/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Server started successfully!")
                    return True
            except requests.exceptions.RequestException:
                time.sleep(1)
                
        print("❌ Server failed to start")
        return False
    
    def step_6_test_api_endpoints(self):
        """Step 6: Test API endpoints"""
        print("\n" + "="*60)
        print("🧪 STEP 6: Testing API Endpoints")
        print("="*60)
        
        # Test health endpoint
        response = requests.get(f"{self.base_url}/health")
        print(f"Health endpoint: {response.status_code}")
        
        # Test API docs
        response = requests.get(f"{self.base_url}/docs")
        print(f"API docs: {response.status_code}")
        
        # Test admin interface
        response = requests.get(f"{self.base_url}/admin/")
        print(f"Admin interface: {response.status_code}")
        
        # Test model endpoints
        response = requests.get(f"{self.api_url}/blog/posts/")
        print(f"Blog posts API: {response.status_code}")
        
        response = requests.get(f"{self.api_url}/blog/categories/")
        print(f"Blog categories API: {response.status_code}")
        
        print("✅ Basic API endpoints tested")
    
    def cleanup(self):
        """Clean up resources"""
        if self.server_process:
            print("🛑 Stopping server...")
            self.server_process.terminate()
            self.server_process.wait()
    
    def run_complete_workflow(self):
        """Run the complete workflow test"""
        print("🧪 Starting Complete FABI+ Workflow Test")
        print("=" * 80)
        
        try:
            self.step_1_create_project()
            self.step_2_create_app()
            self.step_3_database_setup()
            self.step_4_create_users()
            
            if self.step_5_start_server():
                self.step_6_test_api_endpoints()
                
                print("\n" + "="*80)
                print("🎉 COMPLETE WORKFLOW TEST PASSED!")
                print("="*80)
                print(f"✅ Project: {self.project_name}")
                print(f"✅ ORM: {self.orm}")
                print(f"✅ Server running at: {self.base_url}")
                print(f"✅ API docs: {self.base_url}/docs")
                print(f"✅ Admin: {self.base_url}/admin")
                print("="*80)
                
                # Keep server running for manual testing
                print("🔄 Server will keep running for manual testing...")
                print("Press Ctrl+C to stop")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n👋 Stopping server...")
            else:
                print("❌ Server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Workflow test failed: {e}")
            return False
        finally:
            self.cleanup()
            
        return True

def main():
    """Main test runner"""
    orm = sys.argv[1] if len(sys.argv) > 1 else "sqlmodel"
    
    if orm not in ["sqlmodel", "sqlalchemy"]:
        print("Usage: python comprehensive_workflow_test.py [sqlmodel|sqlalchemy]")
        sys.exit(1)
    
    tester = FABIPlusWorkflowTest(orm=orm)
    success = tester.run_complete_workflow()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
