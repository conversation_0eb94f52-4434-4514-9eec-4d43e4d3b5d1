# FABI+ Framework

**A production-ready, modular, extensible API-only Python framework**

FABI+ combines the async speed and documentation power of FastAPI with the admin robustness and structure of Django, providing automatic settings management, built-in admin dashboard, and no template rendering - strictly API-focused.

## 🚀 Features

### Core Framework
- **High Performance**: Built on FastAPI with async support
- **Auto-Generated APIs**: Automatic CRUD endpoints for all models
- **Authentication**: OAuth2/JWT authentication with custom backend support
- **Security**: CSRF protection, XSS protection, CORS, and security headers
- **Database**: SQLModel/SQLAlchemy with automatic migrations
- **Caching**: Multi-backend caching (memory, Redis, file)
- **CLI Management**: Django-style management commands
- **Plugin System**: Extensible plugin architecture
- **Production Ready**: Docker support, monitoring, and deployment tools

### 🎯 Enterprise Admin Interface (Production Ready)
- **📊 Real-time Activity Monitoring**: Live activity tracking with database integration
- **📈 Analytics Dashboard**: Interactive charts showing user activity and system metrics
- **🔒 Security Monitoring**: Failed login tracking, IP monitoring, and threat detection
- **📋 Live Server Logs**: Real-time log streaming via WebSocket connections
- **⚙️ System Settings**: Configuration management with environment integration
- **👥 User Management**: Role-based access control with superuser capabilities
- **🔍 Advanced Filtering**: Time-based filters and comprehensive search
- **📱 Responsive Design**: Modern Bootstrap UI that works on all devices

## 📦 Installation

```bash
# Clone or create your project
git clone <your-repo>
cd your-project

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings

# Create database tables
python manage.py createtables

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

## 🏗️ Quick Start

### 1. Define Your Models

```python
from fabiplus.core.models import BaseModel, register_model
from sqlmodel import Field
from typing import Optional

@register_model
class Blog(BaseModel, table=True):
    title: str = Field(max_length=200)
    content: str
    published: bool = Field(default=False)
    author_id: Optional[str] = None
    
    def __str__(self):
        return self.title
```

### 2. Run Your Application

```python
from fabiplus.core.app import create_app

app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
```

### 3. Access Your API

- **API Documentation**: http://localhost:8000/docs
- **Admin Dashboard**: http://localhost:8000/admin/
- **Live Activity Monitoring**: http://localhost:8000/admin/activities/
- **Analytics Dashboard**: http://localhost:8000/admin/analytics/
- **Security Monitoring**: http://localhost:8000/admin/security/
- **Live Server Logs**: http://localhost:8000/admin/logs/
- **System Settings**: http://localhost:8000/admin/settings/
- **Auto-generated API**: http://localhost:8000/api/blog/

## 🔧 Configuration

FABI+ uses Pydantic settings with `.env` file support:

```bash
# .env
APP_NAME="My API"
DEBUG=true
DATABASE_URL="postgresql://user:pass@localhost/db"
SECRET_KEY="your-secret-key"
CORS_ORIGINS=["http://localhost:3000"]
```

## 📚 Core Concepts

### Models

All models inherit from `BaseModel` and are automatically registered:

```python
@register_model
class Product(BaseModel, table=True):
    name: str = Field(max_length=100)
    price: float = Field(gt=0)
    category_id: Optional[uuid.UUID] = None
```

### Auto-Generated APIs

Every registered model gets full CRUD endpoints:

- `GET /api/product/` - List products (with pagination, filtering, sorting)
- `GET /api/product/{id}/` - Get specific product
- `POST /api/product/` - Create product
- `PUT /api/product/{id}/` - Update product
- `DELETE /api/product/{id}/` - Delete product

### Authentication

```python
# Login
POST /auth/login
{
    "username": "admin",
    "password": "password"
}

# Use token in requests
Authorization: Bearer <token>
```

### 🎯 Enterprise Admin Interface

Access the comprehensive admin interface at `/admin/` with staff user credentials:

#### Core Admin Features
- **Model Management**: Full CRUD operations for all registered models
- **User Management**: Role-based access control and permissions
- **Bulk Actions**: Perform operations on multiple records
- **Advanced Filtering**: Search and filter across all model fields

#### 📊 Real-time Monitoring (Production Ready)
- **Activity Monitoring**: Live tracking of all user actions and API requests
- **Analytics Dashboard**: Interactive charts with real database data
- **Security Monitoring**: Failed login tracking and threat detection
- **Live Server Logs**: WebSocket real-time log streaming
- **System Settings**: Environment and configuration management

#### Key Admin URLs
- **Main Dashboard**: `/admin/` - Overview and model management
- **Activities**: `/admin/activities/` - Real-time activity monitoring
- **Analytics**: `/admin/analytics/` - Interactive charts and statistics
- **Security**: `/admin/security/` - Security monitoring and alerts
- **Logs**: `/admin/logs/` - Live server log streaming
- **Settings**: `/admin/settings/` - System configuration

## 🛠️ Management Commands

```bash
# Run development server
python manage.py runserver

# Create superuser
python manage.py createsuperuser

# Create database tables
python manage.py createtables

# Show registered models
python manage.py showmodels

# Interactive shell
python manage.py shell

# Show configuration
python manage.py config
```

## 🔐 Authentication & Security

### Custom Authentication Backend

```python
# settings.py or .env
AUTH_BACKEND = "myapp.auth.CustomAuthBackend"

# myapp/auth.py
from fabiplus.core.auth import BaseAuthBackend

class CustomAuthBackend(BaseAuthBackend):
    def authenticate_user(self, username, password):
        # Custom authentication logic
        pass
```

### Permissions

```python
from fabiplus.core.auth import has_permission

@app.get("/protected/")
async def protected_endpoint(user = Depends(has_permission("admin"))):
    return {"message": "Admin only"}
```

## 🧪 Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=fabiplus

# Run specific test
pytest fabiplus/tests/test_basic.py::test_login_endpoint
```

## 🔌 Plugins

Create plugins to extend functionality:

```python
# myplugin.py
def register_plugin(app):
    @app.get("/plugin/hello")
    async def hello():
        return {"message": "Hello from plugin!"}

# settings.py
INSTALLED_PLUGINS = ["myplugin"]
```

## 📊 Monitoring & Logging

FABI+ includes comprehensive logging and monitoring:

- Request/response logging
- Performance monitoring
- Error tracking
- Security event logging

## 🐳 Production Deployment

### Docker

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["python", "manage.py", "runserver", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Variables

```bash
# Production settings
ENVIRONMENT=production
DEBUG=false
DATABASE_URL=postgresql://...
SECRET_KEY=your-production-secret
CORS_ORIGINS=["https://yourdomain.com"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- Documentation: [Coming Soon]
- Issues: GitHub Issues
- Discussions: GitHub Discussions

---

**FABI+ Framework** - Built for developers who want Django's structure with FastAPI's speed.
