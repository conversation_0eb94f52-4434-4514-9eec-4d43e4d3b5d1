#!/usr/bin/env python3
"""
Comprehensive FABI+ Framework Test Suite
Tests all framework components: project creation, migrations, permissions, API, admin
"""

import os
import shutil
import subprocess
import sys
import tempfile
import time
from pathlib import Path
from typing import Any, Dict

import pytest
import requests


class FABIPlusFrameworkTest:
    """Comprehensive test suite for FABI+ framework"""

    def __init__(self):
        self.base_dir = Path.cwd()
        self.test_dir = None
        self.project_name = "test_framework_project"
        self.server_process = None
        self.base_url = "http://localhost:8001"  # Different port to avoid conflicts

    def setup(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        print(f"Test directory: {self.test_dir}")

    def teardown(self):
        """Clean up test environment"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()

        if self.test_dir and self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def run_command(
        self, command: str, cwd: Path = None
    ) -> subprocess.CompletedProcess:
        """Run a command and return result"""
        if cwd is None:
            cwd = (
                self.test_dir / self.project_name
                if (self.test_dir / self.project_name).exists()
                else self.test_dir
            )

        print(f"Running: {command} in {cwd}")
        result = subprocess.run(
            command.split(), cwd=cwd, capture_output=True, text=True
        )

        if result.returncode != 0:
            print(f"Command failed: {result.stderr}")

        return result

    def test_project_creation(self):
        """Test project creation with different options"""
        print("\n=== Testing Project Creation ===")

        # Test SQLModel project
        result = self.run_command(
            f"fabiplus project startproject {self.project_name} --orm sqlmodel --auth oauth2",
            cwd=self.test_dir,
        )
        assert result.returncode == 0, f"Project creation failed: {result.stderr}"

        project_dir = self.test_dir / self.project_name
        assert project_dir.exists(), "Project directory not created"

        # Check essential files
        essential_files = [
            "manage.py",
            "pyproject.toml",
            f"{self.project_name}/settings.py",
            f"{self.project_name}/urls.py",
            "apps/__init__.py",
        ]

        for file_path in essential_files:
            file_full_path = project_dir / file_path
            assert file_full_path.exists(), f"Essential file missing: {file_path}"

        print("✅ Project creation test passed")

    def test_app_creation(self):
        """Test app creation"""
        print("\n=== Testing App Creation ===")

        # Create blog app
        process = subprocess.Popen(
            ["fabiplus", "app", "startapp", "blog"],
            cwd=self.test_dir / self.project_name,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        stdout, stderr = process.communicate(input="y\n")
        assert process.returncode == 0, f"App creation failed: {stderr}"

        # Check app files
        app_dir = self.test_dir / self.project_name / "apps" / "blog"
        app_files = ["models.py", "views.py", "admin.py", "serializers.py"]

        for file_name in app_files:
            file_path = app_dir / file_name
            assert file_path.exists(), f"App file missing: {file_name}"

        print("✅ App creation test passed")

    def test_database_migrations(self):
        """Test database migrations"""
        print("\n=== Testing Database Migrations ===")

        # Add models to blog app
        models_file = self.test_dir / self.project_name / "apps" / "blog" / "models.py"
        models_content = """
@register_model
class Post(BaseModel, table=True):
    title: str = Field(max_length=200)
    content: str = Field()
    is_published: bool = Field(default=False)

@register_model  
class Category(BaseModel, table=True):
    name: str = Field(max_length=100)
    description: str = Field(default="")
"""

        with open(models_file, "a") as f:
            f.write(models_content)

        # Create migrations
        result = self.run_command(
            'fabiplus db makemigrations --message "Test migration"'
        )
        assert result.returncode == 0, f"Migration creation failed: {result.stderr}"

        # Run migrations
        result = self.run_command("fabiplus db migrate")
        assert result.returncode == 0, f"Migration failed: {result.stderr}"

        print("✅ Database migrations test passed")

    def test_user_management(self):
        """Test user creation and management"""
        print("\n=== Testing User Management ===")

        # Create users
        users = [
            ("admin", "<EMAIL>", "admin123", "--superuser"),
            ("staff", "<EMAIL>", "staff123", "--staff"),
            ("user", "<EMAIL>", "user123", ""),
        ]

        for username, email, password, flags in users:
            command = f"fabiplus user create --username {username} --email {email} --password {password} {flags}".strip()
            result = self.run_command(command)
            assert (
                result.returncode == 0
            ), f"User creation failed for {username}: {result.stderr}"

        print("✅ User management test passed")

    def test_server_startup(self):
        """Test server startup"""
        print("\n=== Testing Server Startup ===")

        # Start server
        self.server_process = subprocess.Popen(
            ["fabiplus", "server", "run", "--host", "127.0.0.1", "--port", "8001"],
            cwd=self.test_dir / self.project_name,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # Wait for server to start
        for i in range(30):
            try:
                response = requests.get(f"{self.base_url}/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Server startup test passed")
                    return
            except requests.exceptions.RequestException:
                time.sleep(1)

        assert False, "Server failed to start within 30 seconds"

    def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n=== Testing API Endpoints ===")

        # Test health endpoint
        response = requests.get(f"{self.base_url}/health")
        assert response.status_code == 200, "Health endpoint failed"

        # Test API docs
        response = requests.get(f"{self.base_url}/docs")
        assert response.status_code == 200, "API docs failed"

        # Test authentication
        auth_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{self.base_url}/auth/token", data=auth_data)
        assert response.status_code == 200, "Authentication failed"

        token = response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}

        # Test model endpoints
        response = requests.get(f"{self.base_url}/api/v1/blog/post/", headers=headers)
        assert response.status_code == 200, "Blog post API failed"

        print("✅ API endpoints test passed")

    def test_permissions_system(self):
        """Test permissions system"""
        print("\n=== Testing Permissions System ===")

        # Test different user access levels
        users = [
            ("admin", "admin123", True),  # Should have access
            ("staff", "staff123", True),  # Should have access
            ("user", "user123", False),  # Might not have access to admin endpoints
        ]

        for username, password, should_have_admin_access in users:
            # Authenticate
            auth_data = {"username": username, "password": password}
            response = requests.post(f"{self.base_url}/auth/token", data=auth_data)
            assert response.status_code == 200, f"Authentication failed for {username}"

            token = response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}

            # Test API access
            response = requests.get(
                f"{self.base_url}/api/v1/blog/post/", headers=headers
            )
            assert response.status_code == 200, f"API access failed for {username}"

        print("✅ Permissions system test passed")

    def test_admin_interface(self):
        """Test admin interface"""
        print("\n=== Testing Admin Interface ===")

        # Test admin interface accessibility
        response = requests.get(f"{self.base_url}/admin/")
        assert response.status_code == 200, "Admin interface not accessible"

        print("✅ Admin interface test passed")

    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting Comprehensive FABI+ Framework Tests")
        print("=" * 80)

        try:
            self.setup()

            # Run tests in order
            self.test_project_creation()
            self.test_app_creation()
            self.test_database_migrations()
            self.test_user_management()
            self.test_server_startup()
            self.test_api_endpoints()
            self.test_permissions_system()
            self.test_admin_interface()

            print("\n" + "=" * 80)
            print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
            print("FABI+ Framework is fully functional and production-ready!")
            print("=" * 80)

            return True

        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            return False
        finally:
            self.teardown()


def main():
    """Main test runner"""
    tester = FABIPlusFrameworkTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
