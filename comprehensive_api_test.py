#!/usr/bin/env python3
"""
Comprehensive API Integration Test for FABI+ Framework
Tests all systems: models, migrations, permissions, media, response types
"""

import asyncio
import json
import requests
import time
import subprocess
import sys
import os
from pathlib import Path

# Test configuration
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api"
ADMIN_URL = f"{BASE_URL}/admin"

class ComprehensiveAPITest:
    """Comprehensive API test suite"""
    
    def __init__(self):
        self.server_process = None
        self.test_results = {}
        
    def start_server(self, project_path: str):
        """Start the FABI+ development server"""
        print(f"🚀 Starting FABI+ server in {project_path}...")
        
        # Change to project directory
        os.chdir(project_path)
        
        # Start server in background
        self.server_process = subprocess.Popen(
            [sys.executable, "-m", "fabiplus", "server", "run", "--host", "0.0.0.0", "--port", "8000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=project_path
        )
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get(f"{BASE_URL}/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Server started successfully!")
                    return True
            except requests.exceptions.RequestException:
                time.sleep(1)
                
        print("❌ Server failed to start")
        return False
    
    def stop_server(self):
        """Stop the FABI+ server"""
        if self.server_process:
            print("🛑 Stopping server...")
            self.server_process.terminate()
            self.server_process.wait()
            
    def test_health_endpoint(self):
        """Test basic health endpoint"""
        print("\n📊 Testing Health Endpoint...")
        try:
            response = requests.get(f"{BASE_URL}/health")
            success = response.status_code == 200
            self.test_results['health'] = success
            print(f"✅ Health endpoint: {response.status_code}" if success else f"❌ Health endpoint failed: {response.status_code}")
            return success
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
            self.test_results['health'] = False
            return False
    
    def test_api_docs(self):
        """Test API documentation endpoints"""
        print("\n📚 Testing API Documentation...")
        try:
            # Test OpenAPI docs
            response = requests.get(f"{BASE_URL}/docs")
            docs_success = response.status_code == 200
            
            # Test OpenAPI JSON
            response = requests.get(f"{BASE_URL}/openapi.json")
            openapi_success = response.status_code == 200
            
            success = docs_success and openapi_success
            self.test_results['api_docs'] = success
            print(f"✅ API docs accessible" if success else f"❌ API docs failed")
            return success
        except Exception as e:
            print(f"❌ API docs error: {e}")
            self.test_results['api_docs'] = False
            return False
    
    def test_admin_interface(self):
        """Test admin interface"""
        print("\n🔧 Testing Admin Interface...")
        try:
            response = requests.get(f"{ADMIN_URL}/")
            success = response.status_code == 200
            self.test_results['admin'] = success
            print(f"✅ Admin interface accessible" if success else f"❌ Admin interface failed: {response.status_code}")
            return success
        except Exception as e:
            print(f"❌ Admin interface error: {e}")
            self.test_results['admin'] = False
            return False
    
    def test_model_crud_api(self):
        """Test CRUD operations on models"""
        print("\n📝 Testing Model CRUD API...")
        try:
            # Test GET all posts
            response = requests.get(f"{API_URL}/blog/posts/")
            get_success = response.status_code == 200
            
            if get_success:
                data = response.json()
                print(f"✅ GET posts: {len(data.get('data', []))} items")
            
            # Test POST create post
            post_data = {
                "title": "Test Post",
                "content": "This is a test post content",
                "is_published": True,
                "author": "Test Author"
            }
            response = requests.post(f"{API_URL}/blog/posts/", json=post_data)
            post_success = response.status_code in [200, 201]
            
            if post_success:
                created_post = response.json()
                post_id = created_post.get('id')
                print(f"✅ POST create post: ID {post_id}")
                
                # Test GET single post
                response = requests.get(f"{API_URL}/blog/posts/{post_id}")
                get_single_success = response.status_code == 200
                
                if get_single_success:
                    print(f"✅ GET single post: {response.json().get('title')}")
                
                # Test PUT update post
                update_data = {**post_data, "title": "Updated Test Post"}
                response = requests.put(f"{API_URL}/blog/posts/{post_id}", json=update_data)
                put_success = response.status_code == 200
                
                if put_success:
                    print(f"✅ PUT update post: {response.json().get('title')}")
                
                # Test DELETE post
                response = requests.delete(f"{API_URL}/blog/posts/{post_id}")
                delete_success = response.status_code in [200, 204]
                
                if delete_success:
                    print(f"✅ DELETE post: removed")
                
                success = get_success and post_success and get_single_success and put_success and delete_success
            else:
                success = get_success
                print(f"❌ POST create failed: {response.status_code}")
            
            self.test_results['crud_api'] = success
            return success
            
        except Exception as e:
            print(f"❌ CRUD API error: {e}")
            self.test_results['crud_api'] = False
            return False
    
    def test_response_formats(self):
        """Test different response formats"""
        print("\n📊 Testing Response Formats...")
        try:
            # Test JSON response (default)
            response = requests.get(f"{API_URL}/blog/posts/")
            json_success = response.status_code == 200 and response.headers.get('content-type', '').startswith('application/json')
            
            if json_success:
                print("✅ JSON response format working")
            
            success = json_success
            self.test_results['response_formats'] = success
            return success
            
        except Exception as e:
            print(f"❌ Response formats error: {e}")
            self.test_results['response_formats'] = False
            return False
    
    def test_pagination(self):
        """Test API pagination"""
        print("\n📄 Testing Pagination...")
        try:
            # Test with pagination parameters
            response = requests.get(f"{API_URL}/blog/posts/?page=1&per_page=5")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                has_pagination = 'pagination' in data or 'total' in data or 'page' in data
                print(f"✅ Pagination working: {has_pagination}")
                success = has_pagination
            
            self.test_results['pagination'] = success
            return success
            
        except Exception as e:
            print(f"❌ Pagination error: {e}")
            self.test_results['pagination'] = False
            return False
    
    def run_all_tests(self, project_path: str):
        """Run all comprehensive tests"""
        print("🧪 Starting Comprehensive FABI+ API Tests")
        print("=" * 50)
        
        # Start server
        if not self.start_server(project_path):
            print("❌ Failed to start server, aborting tests")
            return False
        
        try:
            # Run all tests
            tests = [
                self.test_health_endpoint,
                self.test_api_docs,
                self.test_admin_interface,
                self.test_model_crud_api,
                self.test_response_formats,
                self.test_pagination,
            ]
            
            for test in tests:
                test()
                time.sleep(1)  # Brief pause between tests
            
            # Print summary
            print("\n" + "=" * 50)
            print("🏁 Test Results Summary:")
            print("=" * 50)
            
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results.values() if result)
            
            for test_name, result in self.test_results.items():
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{test_name:20} {status}")
            
            print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
            
            if passed_tests == total_tests:
                print("🎉 ALL TESTS PASSED! FABI+ framework is working perfectly!")
                return True
            else:
                print(f"⚠️  {total_tests - passed_tests} tests failed")
                return False
                
        finally:
            self.stop_server()

def main():
    """Main test runner"""
    if len(sys.argv) != 2:
        print("Usage: python comprehensive_api_test.py <project_path>")
        print("Example: python comprehensive_api_test.py testproject_sqlmodel")
        sys.exit(1)
    
    project_path = sys.argv[1]
    if not os.path.exists(project_path):
        print(f"❌ Project path does not exist: {project_path}")
        sys.exit(1)
    
    tester = ComprehensiveAPITest()
    success = tester.run_all_tests(project_path)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
