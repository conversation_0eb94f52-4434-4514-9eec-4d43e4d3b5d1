"""
Testproject_Sqlalchemy Settings
FABI+ project configuration with SQLALCHEMY backend
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Project specific settings
APP_NAME = "Testproject_Sqlalchemy API"
DEBUG = True

# ORM Backend Configuration
ORM_BACKEND = "sqlalchemy"

"""
Database settings for SQLAlchemy
Generated by FABI+ framework
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./testproject_sqlalchemy.db")

# SQLAlchemy specific settings
SQLALCHEMY_DATABASE_URL = DATABASE_URL
SQLALCHEMY_ENGINE_OPTIONS = {
    "echo": DEBUG,
    "pool_pre_ping": True,
    "pool_recycle": 3600,
}

# Migration settings
ALEMBIC_CONFIG = {
    "script_location": "migrations",
    "file_template": "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s",
    "timezone": "UTC",
}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]


# Installed Apps
INSTALLED_APPS = [
    "apps.core",
    # Add your apps here:
    # "apps.blog",
    # "apps.users",
    "apps.blog",
]

# API Configuration
API_PREFIX = "/api/v1"

# Admin Configuration
ADMIN_ENABLED = True
ADMIN_PREFIX = "/admin"

# Security
SECRET_KEY = "testproject_sqlalchemy-dev-secret-key-change-in-production"

# CORS
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]