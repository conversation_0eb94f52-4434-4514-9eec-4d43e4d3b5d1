"""
Blog Models
Database models for the blog app (SQLAlchemy backend)
"""

from typing import Optional
from sqlalchemy import Column, Integer, String, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from fabiplus.core.models import BaseModel, register_model

Base = declarative_base()


@register_model
class Post(Base):
    """Blog post model"""
    __tablename__ = "blog_posts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, comment="Post title")
    content = Column(Text, nullable=False, comment="Post content")
    is_published = Column(Boolean, default=False, comment="Is published")
    author = Column(String(100), nullable=True, comment="Author name")

    def __str__(self):
        return self.title


@register_model
class Category(Base):
    """Blog category model"""
    __tablename__ = "blog_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="Category name")
    description = Column(Text, nullable=True, default="", comment="Category description")
    is_active = Column(Boolean, default=True, comment="Is active")

    def __str__(self):
        return self.name