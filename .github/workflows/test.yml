name: FABI+ Framework Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.10", "3.11", "3.12"]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run basic tests
      run: |
        poetry run pytest fabiplus/tests/test_basic.py -v
    
    - name: Run template tests
      run: |
        poetry run pytest fabiplus/tests/test_templates.py -v
    
    - name: Run CLI template tests
      run: |
        poetry run pytest fabiplus/tests/test_cli_templates.py -v
    
    - name: Run ORM choice tests (skipped for now)
      run: |
        poetry run pytest fabiplus/tests/test_orm_choice.py -v
    
    - name: Test CLI commands
      run: |
        poetry run python -m fabiplus.cli.main --help
        poetry run python -m fabiplus.cli.main project --help
        poetry run python -m fabiplus.cli.main app --help
        poetry run python -m fabiplus.cli.main db --help
    
    - name: Test project creation
      run: |
        cd /tmp
        poetry run python -m fabiplus.cli.main project startproject testproject --template default
        cd testproject
        ls -la
        cat pyproject.toml
        cat manage.py
    
    - name: Test app creation
      run: |
        cd /tmp/testproject
        poetry run python -m fabiplus.cli.main app startapp testapp
        ls -la apps/testapp/
        cat apps/testapp/models.py
        cat apps/testapp/views.py
    
    - name: Test project with Docker
      run: |
        cd /tmp
        poetry run python -m fabiplus.cli.main project startproject dockertest --docker
        cd dockertest
        ls -la
        test -f Dockerfile
        test -f docker-compose.yml
    
    - name: Run linting
      run: |
        poetry run flake8 fabiplus --count --select=E9,F63,F7,F82 --show-source --statistics
        poetry run flake8 fabiplus --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  test-coverage:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: poetry install --no-interaction
    
    - name: Run tests with coverage
      run: |
        poetry run pytest --cov=fabiplus --cov-report=xml --cov-report=html fabiplus/tests/
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  test-integration:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: fabiplus_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies with database drivers
      run: |
        poetry install --no-interaction
        poetry add psycopg2-binary redis
    
    - name: Test with PostgreSQL
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/fabiplus_test
        REDIS_URL: redis://localhost:6379/0
      run: |
        cd /tmp
        poetry run python -m fabiplus.cli.main project startproject pgtest
        cd pgtest
        echo 'DATABASE_URL="postgresql://postgres:postgres@localhost:5432/fabiplus_test"' > .env
        echo 'REDIS_URL="redis://localhost:6379/0"' >> .env
        poetry install
        poetry run python manage.py db createtables
    
    - name: Test caching with Redis
      env:
        REDIS_URL: redis://localhost:6379/0
      run: |
        cd /tmp/pgtest
        poetry run python -m fabiplus.cli.main cache clear
        poetry run python -m fabiplus.cli.main cache stats

  test-docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Test Docker project creation
      run: |
        cd /tmp
        python -m pip install poetry
        poetry install
        poetry run python -m fabiplus.cli.main project startproject dockertest --docker
        cd dockertest
    
    - name: Build Docker image
      run: |
        cd /tmp/dockertest
        docker build -t fabiplus-test .
    
    - name: Test Docker compose
      run: |
        cd /tmp/dockertest
        docker-compose config

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: poetry install --no-interaction
    
    - name: Run security scan with bandit
      run: |
        poetry add bandit[toml]
        poetry run bandit -r fabiplus -f json -o bandit-report.json
      continue-on-error: true
    
    - name: Run safety check
      run: |
        poetry add safety
        poetry run safety check
      continue-on-error: true
