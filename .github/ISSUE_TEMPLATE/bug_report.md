---
name: Bug report
about: Create a report to help us improve FABI+
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📷 Screenshots

If applicable, add screenshots to help explain your problem.

## 🖥️ Environment

**Desktop (please complete the following information):**
- OS: [e.g. Ubuntu 22.04, Windows 11, macOS 13]
- Python Version: [e.g. 3.10.5]
- FABI+ Version: [e.g. 1.0.0]
- Database: [e.g. PostgreSQL 15, SQLite]

**Additional Environment Details:**
- Virtual Environment: [e.g. venv, conda, poetry]
- Installation Method: [e.g. pip, poetry, from source]

## 📋 Code Sample

```python
# Minimal code sample that reproduces the issue
# Please include relevant imports and setup
```

## 📄 Configuration

```python
# Relevant settings.py or configuration
# Remove sensitive information
```

## 📊 Logs

```
# Relevant log output
# Include stack traces if available
```

## 🔍 Additional Context

Add any other context about the problem here.

## ✅ Checklist

- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have provided a minimal code sample that reproduces the issue
- [ ] I have included relevant environment information
- [ ] I have included relevant logs or error messages
- [ ] I have tested this with the latest version of FABI+
