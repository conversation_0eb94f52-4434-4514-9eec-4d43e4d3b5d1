#!/usr/bin/env python3
"""
Comprehensive API and Permissions Test for FABI+ Framework
Tests authentication, permissions, response types, and streaming
"""

import csv
import io
import json
import time
from typing import Any, Dict

import requests


class FABIPlusAPITest:
    """Comprehensive API test suite"""

    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        self.admin_url = f"{base_url}/admin"
        self.tokens = {}
        self.test_results = {}

    def authenticate_user(self, username: str, password: str) -> str:
        """Authenticate user and get token"""
        print(f"🔐 Authenticating user: {username}")

        # Try to get token
        auth_data = {"username": username, "password": password}

        response = requests.post(f"{self.base_url}/auth/token", data=auth_data)

        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            self.tokens[username] = token
            print(f"✅ Authentication successful for {username}")
            return token
        else:
            print(f"❌ Authentication failed for {username}: {response.status_code}")
            print(f"Response: {response.text}")
            return None

    def get_headers(self, username: str = None) -> Dict[str, str]:
        """Get headers with authentication token"""
        headers = {"Content-Type": "application/json"}
        if username and username in self.tokens:
            headers["Authorization"] = f"Bearer {self.tokens[username]}"
        return headers

    def test_health_and_docs(self):
        """Test basic endpoints"""
        print("\n" + "=" * 60)
        print("🏥 Testing Health and Documentation Endpoints")
        print("=" * 60)

        # Test health endpoint
        response = requests.get(f"{self.base_url}/health")
        health_ok = response.status_code == 200
        print(f"Health endpoint: {'✅' if health_ok else '❌'} {response.status_code}")

        # Test API docs
        response = requests.get(f"{self.base_url}/docs")
        docs_ok = response.status_code == 200
        print(f"API docs: {'✅' if docs_ok else '❌'} {response.status_code}")

        # Test OpenAPI JSON
        response = requests.get(f"{self.base_url}/openapi.json")
        openapi_ok = response.status_code == 200
        print(f"OpenAPI JSON: {'✅' if openapi_ok else '❌'} {response.status_code}")

        # Test admin interface
        response = requests.get(f"{self.admin_url}/")
        admin_ok = response.status_code == 200
        print(f"Admin interface: {'✅' if admin_ok else '❌'} {response.status_code}")

        self.test_results["basic_endpoints"] = (
            health_ok and docs_ok and openapi_ok and admin_ok
        )

    def test_authentication(self):
        """Test user authentication"""
        print("\n" + "=" * 60)
        print("🔐 Testing Authentication")
        print("=" * 60)

        # Test all users
        users = [("admin", "admin123"), ("staff", "staff123"), ("user", "user123")]

        auth_success = True
        for username, password in users:
            token = self.authenticate_user(username, password)
            if not token:
                auth_success = False

        self.test_results["authentication"] = auth_success

    def test_model_crud_operations(self):
        """Test CRUD operations on models"""
        print("\n" + "=" * 60)
        print("📝 Testing Model CRUD Operations")
        print("=" * 60)

        # Test as admin user
        headers = self.get_headers("admin")

        # Test GET all posts
        response = requests.get(f"{self.api_url}/blog/post/", headers=headers)
        get_all_ok = response.status_code == 200
        print(f"GET all posts: {'✅' if get_all_ok else '❌'} {response.status_code}")

        if get_all_ok:
            data = response.json()
            print(f"  Found {len(data.get('data', data))} posts")

        # Test POST create post
        post_data = {
            "title": "Test Post via API",
            "content": "This is a test post created via API",
            "is_published": True,
            "author": "admin",
        }

        response = requests.post(
            f"{self.api_url}/blog/post/", json=post_data, headers=headers
        )
        create_ok = response.status_code in [200, 201]
        print(f"POST create post: {'✅' if create_ok else '❌'} {response.status_code}")

        post_id = None
        if create_ok:
            created_post = response.json()
            post_id = created_post.get("id")
            print(f"  Created post ID: {post_id}")

            # Test GET single post
            response = requests.get(
                f"{self.api_url}/blog/post/{post_id}", headers=headers
            )
            get_single_ok = response.status_code == 200
            print(
                f"GET single post: {'✅' if get_single_ok else '❌'} {response.status_code}"
            )

            # Test PUT update post
            update_data = {**post_data, "title": "Updated Test Post via API"}
            response = requests.put(
                f"{self.api_url}/blog/post/{post_id}", json=update_data, headers=headers
            )
            update_ok = response.status_code == 200
            print(
                f"PUT update post: {'✅' if update_ok else '❌'} {response.status_code}"
            )

            # Test DELETE post
            response = requests.delete(
                f"{self.api_url}/blog/post/{post_id}", headers=headers
            )
            delete_ok = response.status_code in [200, 204]
            print(f"DELETE post: {'✅' if delete_ok else '❌'} {response.status_code}")

            crud_success = (
                get_all_ok and create_ok and get_single_ok and update_ok and delete_ok
            )
        else:
            crud_success = get_all_ok
            print(f"  Error creating post: {response.text}")

        self.test_results["crud_operations"] = crud_success

    def test_permissions_levels(self):
        """Test different permission levels"""
        print("\n" + "=" * 60)
        print("🔒 Testing Permission Levels")
        print("=" * 60)

        # Create a test post as admin
        admin_headers = self.get_headers("admin")
        post_data = {
            "title": "Permission Test Post",
            "content": "This post is for testing permissions",
            "is_published": True,
            "author": "admin",
        }

        response = requests.post(
            f"{self.api_url}/blog/post/", json=post_data, headers=admin_headers
        )
        if response.status_code in [200, 201]:
            post_id = response.json().get("id")
            print(f"✅ Created test post: {post_id}")

            # Test admin access (should work)
            response = requests.get(
                f"{self.api_url}/blog/post/{post_id}", headers=admin_headers
            )
            admin_access = response.status_code == 200
            print(
                f"Admin access: {'✅' if admin_access else '❌'} {response.status_code}"
            )

            # Test staff access (should work)
            staff_headers = self.get_headers("staff")
            response = requests.get(
                f"{self.api_url}/blog/post/{post_id}", headers=staff_headers
            )
            staff_access = response.status_code == 200
            print(
                f"Staff access: {'✅' if staff_access else '❌'} {response.status_code}"
            )

            # Test regular user access (should work for read)
            user_headers = self.get_headers("user")
            response = requests.get(
                f"{self.api_url}/blog/post/{post_id}", headers=user_headers
            )
            user_read_access = response.status_code == 200
            print(
                f"User read access: {'✅' if user_read_access else '❌'} {response.status_code}"
            )

            # Test regular user write access (might be restricted)
            update_data = {**post_data, "title": "User Updated Post"}
            response = requests.put(
                f"{self.api_url}/blog/post/{post_id}",
                json=update_data,
                headers=user_headers,
            )
            user_write_access = response.status_code == 200
            print(
                f"User write access: {'✅' if user_write_access else '❌'} {response.status_code}"
            )

            # Clean up
            requests.delete(
                f"{self.api_url}/blog/post/{post_id}", headers=admin_headers
            )

            permissions_ok = admin_access and staff_access and user_read_access
        else:
            print(f"❌ Failed to create test post: {response.status_code}")
            permissions_ok = False

        self.test_results["permissions"] = permissions_ok

    def test_response_formats(self):
        """Test different response formats"""
        print("\n" + "=" * 60)
        print("📊 Testing Response Formats")
        print("=" * 60)

        headers = self.get_headers("admin")

        # Test JSON response (default)
        response = requests.get(f"{self.api_url}/blog/post/", headers=headers)
        json_ok = (
            response.status_code == 200
            and "application/json" in response.headers.get("content-type", "")
        )
        print(f"JSON response: {'✅' if json_ok else '❌'} {response.status_code}")

        # Test pagination
        response = requests.get(
            f"{self.api_url}/blog/post/?page=1&per_page=5", headers=headers
        )
        pagination_ok = response.status_code == 200
        if pagination_ok:
            data = response.json()
            has_pagination_info = any(
                key in data for key in ["pagination", "total", "page", "per_page"]
            )
            print(
                f"Pagination: {'✅' if has_pagination_info else '❌'} (has pagination info: {has_pagination_info})"
            )
        else:
            print(f"Pagination: ❌ {response.status_code}")

        self.test_results["response_formats"] = json_ok and pagination_ok

    def test_streaming_responses(self):
        """Test streaming response capabilities"""
        print("\n" + "=" * 60)
        print("🌊 Testing Streaming Responses")
        print("=" * 60)

        headers = self.get_headers("admin")

        # Create multiple posts for streaming test
        print("Creating test data for streaming...")
        post_ids = []
        for i in range(5):
            post_data = {
                "title": f"Streaming Test Post {i+1}",
                "content": f"Content for streaming test post {i+1}",
                "is_published": True,
                "author": "admin",
            }
            response = requests.post(
                f"{self.api_url}/blog/post/", json=post_data, headers=headers
            )
            if response.status_code in [200, 201]:
                post_ids.append(response.json().get("id"))

        print(f"✅ Created {len(post_ids)} test posts")

        # Test large dataset retrieval
        response = requests.get(f"{self.api_url}/blog/post/", headers=headers)
        streaming_ok = response.status_code == 200
        print(
            f"Large dataset retrieval: {'✅' if streaming_ok else '❌'} {response.status_code}"
        )

        # Clean up test posts
        for post_id in post_ids:
            requests.delete(f"{self.api_url}/blog/post/{post_id}", headers=headers)

        self.test_results["streaming"] = streaming_ok

    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting Comprehensive FABI+ API & Permissions Tests")
        print("=" * 80)

        try:
            self.test_health_and_docs()
            self.test_authentication()
            self.test_model_crud_operations()
            self.test_permissions_levels()
            self.test_response_formats()
            self.test_streaming_responses()

            # Print summary
            print("\n" + "=" * 80)
            print("🏁 Test Results Summary")
            print("=" * 80)

            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results.values() if result)

            for test_name, result in self.test_results.items():
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{test_name:20} {status}")

            print(f"\nOverall: {passed_tests}/{total_tests} tests passed")

            if passed_tests == total_tests:
                print("🎉 ALL TESTS PASSED! FABI+ framework is working perfectly!")
                return True
            else:
                print(f"⚠️  {total_tests - passed_tests} tests failed")
                return False

        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return False


def main():
    """Main test runner"""
    tester = FABIPlusAPITest()
    success = tester.run_all_tests()

    if success:
        print("\n🎯 Next Steps for Manual Testing:")
        print("1. Visit http://localhost:8000/docs for API documentation")
        print("2. Visit http://localhost:8000/admin for admin interface")
        print("3. Test authentication with:")
        print("   - admin/admin123 (superuser)")
        print("   - staff/staff123 (staff user)")
        print("   - user/user123 (regular user)")

    return success


if __name__ == "__main__":
    main()
