{"errors": [], "generated_at": "2025-07-16T12:53:25Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 381, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 3, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 5, "SEVERITY.LOW": 377, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 16092, "nosec": 0, "skipped_tests": 0}, "fabiplus/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 11, "nosec": 0, "skipped_tests": 0}, "fabiplus/admin/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/admin/logs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 262, "nosec": 0, "skipped_tests": 0}, "fabiplus/admin/routes.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 426, "nosec": 0, "skipped_tests": 0}, "fabiplus/admin/ui.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1185, "nosec": 0, "skipped_tests": 0}, "fabiplus/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/api/auto.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 319, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/app.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 627, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/cache.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 239, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/database.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 192, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/dev.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 6, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 179, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/project.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 338, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/server.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 62, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/commands/user.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 188, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/templates/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/templates/app.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 370, "nosec": 0, "skipped_tests": 0}, "fabiplus/cli/templates/project.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 445, "nosec": 0, "skipped_tests": 0}, "fabiplus/conf/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/conf/settings.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/activity.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/app.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 448, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/apps.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 103, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/cache.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 229, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/custom_auth.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 219, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/docs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 249, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 210, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 208, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/processors.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 292, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/routes.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 354, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/storage.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 257, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/media/validators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 324, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/migrations.py": {"CONFIDENCE.HIGH": 15, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 15, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 408, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 255, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/orm/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/orm/base.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 130, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/orm/sqlalchemy.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/orm/sqlmodel.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/orm/tortoise.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 57, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/base.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 301, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/checkers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 369, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/decorators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 337, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 317, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/permissions/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 210, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/responses/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/responses/formats.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 312, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/responses/pagination.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 307, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/responses/streaming.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 329, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/user_model.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 77, "nosec": 0, "skipped_tests": 0}, "fabiplus/core/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 276, "nosec": 0, "skipped_tests": 0}, "fabiplus/middleware/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/middleware/activity.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 257, "nosec": 0, "skipped_tests": 0}, "fabiplus/middleware/logging.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 180, "nosec": 0, "skipped_tests": 0}, "fabiplus/middleware/security.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 226, "nosec": 0, "skipped_tests": 0}, "fabiplus/plugins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/plugins/analytics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 129, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_basic.py": {"CONFIDENCE.HIGH": 29, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 31, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 115, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_cli_templates.py": {"CONFIDENCE.HIGH": 47, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 47, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 299, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_media_system.py": {"CONFIDENCE.HIGH": 49, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 49, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_orm_choice.py": {"CONFIDENCE.HIGH": 18, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 18, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 210, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_permissions_system.py": {"CONFIDENCE.HIGH": 63, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 63, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 393, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_response_system.py": {"CONFIDENCE.HIGH": 53, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 53, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 319, "nosec": 0, "skipped_tests": 0}, "fabiplus/tests/test_templates.py": {"CONFIDENCE.HIGH": 78, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 78, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 241, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "142             Path(\"/var/log/fabiplus\"),\n143             Path(\"/tmp/fabiplus_logs\"),\n144             Path.cwd(),  # Current directory as fallback\n", "col_offset": 17, "end_col_offset": 37, "filename": "fabiplus/admin/logs.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 143, "line_range": [143], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "1568             await websocket.close()\n1569         except Exception:\n1570             pass\n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/admin/ui.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 1569, "line_range": [1569, 1570], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "154                 models_count = content.count(\"class \") - content.count(\"class Meta\")\n155         except Exception:\n156             pass\n157 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/cli/commands/app.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 155, "line_range": [155, 156], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "793                     return True\n794         except Exception:\n795             pass\n796 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/cli/commands/app.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 794, "line_range": [794, 795], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "845 \n846                 except Exception:\n847                     continue\n848 \n", "col_offset": 16, "end_col_offset": 28, "filename": "fabiplus/cli/commands/app.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 846, "line_range": [846, 847], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "295             for _ in range(num_operations):\n296                 key = \"\".join(random.choices(string.ascii_letters, k=key_length))\n297                 value = \"\".join(random.choices(string.ascii_letters, k=value_size))\n", "col_offset": 30, "end_col_offset": 80, "filename": "fabiplus/cli/commands/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "296                 key = \"\".join(random.choices(string.ascii_letters, k=key_length))\n297                 value = \"\".join(random.choices(string.ascii_letters, k=value_size))\n298                 test_keys.append(f\"benchmark:{key}\")\n", "col_offset": 32, "end_col_offset": 82, "filename": "fabiplus/cli/commands/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "149 \n150     import subprocess\n151 \n", "col_offset": 4, "end_col_offset": 21, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "165     try:\n166         result = subprocess.run(cmd, check=True)\n167         console.print(\"[green]✅ Tests completed successfully[/green]\")\n", "col_offset": 17, "end_col_offset": 48, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 166, "line_range": [166], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "176 \n177     import subprocess\n178 \n", "col_offset": 4, "end_col_offset": 21, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 177, "line_range": [177], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "188         try:\n189             subprocess.run(cmd, check=True, capture_output=True)\n190             console.print(f\"[green]✅ {tool_name} passed[/green]\")\n", "col_offset": 12, "end_col_offset": 64, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 189, "line_range": [189], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "200 \n201     import subprocess\n202 \n", "col_offset": 4, "end_col_offset": 21, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 201, "line_range": [201], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "211         try:\n212             subprocess.run(cmd, check=True)\n213             console.print(f\"[green]✅ {tool_name} completed[/green]\")\n", "col_offset": 12, "end_col_offset": 43, "filename": "fabiplus/cli/commands/dev.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 212, "line_range": [212], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "25         self.orm_backend = orm_backend\n26         self.jinja_env = Environment(loader=BaseLoader())\n27 \n", "col_offset": 25, "end_col_offset": 57, "filename": "fabiplus/cli/templates/app.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 94, "link": "https://cwe.mitre.org/data/definitions/94.html"}, "issue_severity": "HIGH", "issue_text": "By default, jinja2 sets autoescape to False. Consider using autoescape=True or use the select_autoescape function to mitigate XSS vulnerabilities.", "line_number": 26, "line_range": [26], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b701_jinja2_autoescape_false.html", "test_id": "B701", "test_name": "jinja2_autoescape_false"}, {"code": "32         self.show_admin_routes = show_admin_routes\n33         self.jinja_env = Environment(loader=BaseLoader())\n34 \n", "col_offset": 25, "end_col_offset": 57, "filename": "fabiplus/cli/templates/project.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 94, "link": "https://cwe.mitre.org/data/definitions/94.html"}, "issue_severity": "HIGH", "issue_text": "By default, jinja2 sets autoescape to False. Consider using autoescape=True or use the select_autoescape function to mitigate XSS vulnerabilities.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b701_jinja2_autoescape_false.html", "test_id": "B701", "test_name": "jinja2_autoescape_false"}, {"code": "238     key_data = f\"{args}:{sorted(kwargs.items())}\"\n239     return hashlib.md5(key_data.encode()).hexdigest()\n240 \n", "col_offset": 11, "end_col_offset": 41, "filename": "fabiplus/core/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 239, "line_range": [239], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "206                 user = await get_current_active_user()\n207             except:\n208                 pass\n209 \n", "col_offset": 12, "end_col_offset": 20, "filename": "fabiplus/core/custom_auth.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 207, "line_range": [207, 208], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "114 \n115         except Exception:\n116             # EXIF extraction is optional, don't fail if it doesn't work\n117             pass\n118 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/core/media/processors.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 115, "line_range": [115, 116, 117], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "162 \n163             except Exception as e:\n164                 # Don't fail entire processing if thumbnail generation fails\n165                 continue\n166 \n", "col_offset": 12, "end_col_offset": 24, "filename": "fabiplus/core/media/processors.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 163, "line_range": [163, 164, 165], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "202 \n203         except Exception as e:\n204             # Variant generation is optional\n205             pass\n206 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/core/media/processors.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 203, "line_range": [203, 204, 205], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "332 \n333         except Exception:\n334             pass\n335 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/core/media/processors.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 333, "line_range": [333, 334], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "384 \n385         except Exception:\n386             pass\n387 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/core/media/processors.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 385, "line_range": [385, 386], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "102         \"\"\"Calculate MD5 and SHA256 hashes\"\"\"\n103         md5_hash = hashlib.md5(content).hexdigest()\n104         sha256_hash = hashlib.sha256(content).hexdigest()\n", "col_offset": 19, "end_col_offset": 39, "filename": "fabiplus/core/media/storage.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 103, "line_range": [103], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "6 import os\n7 import subprocess\n8 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 7, "line_range": [7], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "122             # Run aerich init-db command\n123             result = subprocess.run(\n124                 [\"aerich\", \"init-db\"], capture_output=True, text=True, cwd=Path.cwd()\n125             )\n126 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 123, "line_range": [123, 124, 125], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "122             # Run aerich init-db command\n123             result = subprocess.run(\n124                 [\"aerich\", \"init-db\"], capture_output=True, text=True, cwd=Path.cwd()\n125             )\n126 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 123, "line_range": [123, 124, 125], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "417                 # Run aerich init-db first\n418                 init_result = subprocess.run(\n419                     [\"aerich\", \"init-db\"],\n420                     capture_output=True,\n421                     text=True,\n422                     cwd=Path.cwd(),\n423                 )\n424 \n", "col_offset": 30, "end_col_offset": 17, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 418, "line_range": [418, 419, 420, 421, 422, 423], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "417                 # Run aerich init-db first\n418                 init_result = subprocess.run(\n419                     [\"aerich\", \"init-db\"],\n420                     capture_output=True,\n421                     text=True,\n422                     cwd=Path.cwd(),\n423                 )\n424 \n", "col_offset": 30, "end_col_offset": 17, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 418, "line_range": [418, 419, 420, 421, 422, 423], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "436 \n437             result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path.cwd())\n438 \n", "col_offset": 21, "end_col_offset": 88, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 437, "line_range": [437], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "470             # Run aerich upgrade command\n471             result = subprocess.run(\n472                 [\"aerich\", \"upgrade\"], capture_output=True, text=True, cwd=Path.cwd()\n473             )\n474 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 471, "line_range": [471, 472, 473], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "470             # Run aerich upgrade command\n471             result = subprocess.run(\n472                 [\"aerich\", \"upgrade\"], capture_output=True, text=True, cwd=Path.cwd()\n473             )\n474 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 471, "line_range": [471, 472, 473], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "510 \n511             result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path.cwd())\n512 \n", "col_offset": 21, "end_col_offset": 88, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 511, "line_range": [511], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "550             # Run aerich history command\n551             result = subprocess.run(\n552                 [\"aerich\", \"history\"], capture_output=True, text=True, cwd=Path.cwd()\n553             )\n554 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 551, "line_range": [551, 552, 553], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "550             # Run aerich history command\n551             result = subprocess.run(\n552                 [\"aerich\", \"history\"], capture_output=True, text=True, cwd=Path.cwd()\n553             )\n554 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 551, "line_range": [551, 552, 553], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "586             # Run aerich heads command\n587             result = subprocess.run(\n588                 [\"aerich\", \"heads\"], capture_output=True, text=True, cwd=Path.cwd()\n589             )\n590 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 587, "line_range": [587, 588, 589], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "586             # Run aerich heads command\n587             result = subprocess.run(\n588                 [\"aerich\", \"heads\"], capture_output=True, text=True, cwd=Path.cwd()\n589             )\n590 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 587, "line_range": [587, 588, 589], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "622             # Run aerich status command\n623             result = subprocess.run(\n624                 [\"aerich\", \"status\"], capture_output=True, text=True, cwd=Path.cwd()\n625             )\n626 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 623, "line_range": [623, 624, 625], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "622             # Run aerich status command\n623             result = subprocess.run(\n624                 [\"aerich\", \"status\"], capture_output=True, text=True, cwd=Path.cwd()\n625             )\n626 \n", "col_offset": 21, "end_col_offset": 13, "filename": "fabiplus/core/migrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 623, "line_range": [623, 624, 625], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "153             # Basic validation\n154             assert temp_instance.name\n155             assert temp_instance.dependencies\n", "col_offset": 12, "end_col_offset": 37, "filename": "fabiplus/core/orm/base.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 154, "line_range": [154], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "154             assert temp_instance.name\n155             assert temp_instance.dependencies\n156             assert temp_instance.get_field_type_mapping()\n", "col_offset": 12, "end_col_offset": 45, "filename": "fabiplus/core/orm/base.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 155, "line_range": [155], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "155             assert temp_instance.dependencies\n156             assert temp_instance.get_field_type_mapping()\n157 \n", "col_offset": 12, "end_col_offset": 57, "filename": "fabiplus/core/orm/base.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 156, "line_range": [156], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "304             # Evaluate condition\n305             result = eval(self.condition, safe_globals, {})\n306             return bool(result)\n", "col_offset": 21, "end_col_offset": 59, "filename": "fabiplus/core/permissions/base.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "MEDIUM", "issue_text": "Use of possibly insecure function - consider using safer ast.literal_eval.", "line_number": 305, "line_range": [305], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b307-eval", "test_id": "B307", "test_name": "blacklist"}, {"code": "403                 # Load from file\n404                 env = Environment(loader=FileSystemLoader(self.template_dir))\n405                 template = env.get_template(self.template_name)\n", "col_offset": 22, "end_col_offset": 77, "filename": "fabiplus/core/responses/formats.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 94, "link": "https://cwe.mitre.org/data/definitions/94.html"}, "issue_severity": "HIGH", "issue_text": "By default, jinja2 sets autoescape to False. Consider using autoescape=True or use the select_autoescape function to mitigate XSS vulnerabilities.", "line_number": 404, "line_range": [404], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b701_jinja2_autoescape_false.html", "test_id": "B701", "test_name": "jinja2_autoescape_false"}, {"code": "7 import json\n8 import xml.etree.ElementTree as ET\n9 from datetime import datetime\n", "col_offset": 0, "end_col_offset": 34, "filename": "fabiplus/core/responses/streaming.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "LOW", "issue_text": "Using xml.etree.ElementTree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace xml.etree.ElementTree with the equivalent defusedxml package, or make sure defusedxml.defuse_stdlib() is called.", "line_number": 8, "line_range": [8], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b405-import-xml-etree", "test_id": "B405", "test_name": "blacklist"}, {"code": "61                         user = session.get(User, user_id)\n62         except Exception:\n63             # If we can't get user, continue without it\n64             pass\n65 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/middleware/activity.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 62, "line_range": [62, 63, 64], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "49     response = client.get(\"/\")\n50     assert response.status_code == 200\n51     data = response.json()\n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 50, "line_range": [50], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "51     data = response.json()\n52     assert \"message\" in data\n53     assert \"version\" in data\n", "col_offset": 4, "end_col_offset": 28, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 52, "line_range": [52], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "52     assert \"message\" in data\n53     assert \"version\" in data\n54 \n", "col_offset": 4, "end_col_offset": 28, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 53, "line_range": [53], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "58     response = client.get(\"/health\")\n59     assert response.status_code == 200\n60     data = response.json()\n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60     data = response.json()\n61     assert data[\"status\"] == \"healthy\"\n62 \n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "65     \"\"\"Test user creation\"\"\"\n66     user = auth_backend.create_user(\n67         username=\"testuser\", email=\"<EMAIL>\", password=\"testpassword123\"\n68     )\n69 \n", "col_offset": 11, "end_col_offset": 5, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'testpassword123'", "line_number": 66, "line_range": [66, 67, 68], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "69 \n70     assert user.username == \"testuser\"\n71     assert user.email == \"<EMAIL>\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 70, "line_range": [70], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "70     assert user.username == \"testuser\"\n71     assert user.email == \"<EMAIL>\"\n72     assert user.is_active is True\n", "col_offset": 4, "end_col_offset": 43, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 71, "line_range": [71], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "71     assert user.email == \"<EMAIL>\"\n72     assert user.is_active is True\n73     assert user.is_staff is False\n", "col_offset": 4, "end_col_offset": 33, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 72, "line_range": [72], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "72     assert user.is_active is True\n73     assert user.is_staff is False\n74     assert user.is_superuser is False\n", "col_offset": 4, "end_col_offset": 33, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73     assert user.is_staff is False\n74     assert user.is_superuser is False\n75 \n", "col_offset": 4, "end_col_offset": 37, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "79     # Create user\n80     user = auth_backend.create_user(\n81         username=\"authtest\", email=\"<EMAIL>\", password=\"testpassword123\"\n82     )\n83 \n", "col_offset": 11, "end_col_offset": 5, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'testpassword123'", "line_number": 80, "line_range": [80, 81, 82], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "85     authenticated_user = auth_backend.authenticate_user(\"authtest\", \"testpassword123\")\n86     assert authenticated_user is not None\n87     assert authenticated_user.username == \"authtest\"\n", "col_offset": 4, "end_col_offset": 41, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 86, "line_range": [86], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "86     assert authenticated_user is not None\n87     assert authenticated_user.username == \"authtest\"\n88 \n", "col_offset": 4, "end_col_offset": 52, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 87, "line_range": [87], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "90     failed_auth = auth_backend.authenticate_user(\"authtest\", \"wrongpassword\")\n91     assert failed_auth is None\n92 \n", "col_offset": 4, "end_col_offset": 30, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 91, "line_range": [91], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "99     token = auth_backend.create_access_token(test_data)\n100     assert isinstance(token, str)\n101     assert len(token) > 0\n", "col_offset": 4, "end_col_offset": 33, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "100     assert isinstance(token, str)\n101     assert len(token) > 0\n102 \n", "col_offset": 4, "end_col_offset": 25, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "104     decoded_data = auth_backend.decode_access_token(token)\n105     assert decoded_data[\"sub\"] == \"test-user-id\"\n106     assert decoded_data[\"username\"] == \"testuser\"\n", "col_offset": 4, "end_col_offset": 48, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 105, "line_range": [105], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "105     assert decoded_data[\"sub\"] == \"test-user-id\"\n106     assert decoded_data[\"username\"] == \"testuser\"\n107 \n", "col_offset": 4, "end_col_offset": 49, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 106, "line_range": [106], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "125 \n126     assert response.status_code == 200\n127     data = response.json()\n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 126, "line_range": [126], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127     data = response.json()\n128     assert \"access_token\" in data\n129     assert data[\"token_type\"] == \"bearer\"\n", "col_offset": 4, "end_col_offset": 33, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "128     assert \"access_token\" in data\n129     assert data[\"token_type\"] == \"bearer\"\n130     assert \"user\" in data\n", "col_offset": 4, "end_col_offset": 41, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 129, "line_range": [129], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "129     assert data[\"token_type\"] == \"bearer\"\n130     assert \"user\" in data\n131 \n", "col_offset": 4, "end_col_offset": 25, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "138     models = ModelRegistry.get_all_models()\n139     assert \"user\" in models\n140     assert models[\"user\"] == User\n", "col_offset": 4, "end_col_offset": 27, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 139, "line_range": [139], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "139     assert \"user\" in models\n140     assert models[\"user\"] == User\n141 \n", "col_offset": 4, "end_col_offset": 33, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 140, "line_range": [140], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "143     user_model = ModelRegistry.get_model(\"user\")\n144     assert user_model == User\n145 \n", "col_offset": 4, "end_col_offset": 29, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 144, "line_range": [144], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "147     model_names = ModelRegistry.get_model_names()\n148     assert \"user\" in model_names\n149 \n", "col_offset": 4, "end_col_offset": 32, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 148, "line_range": [148], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "153     response = client.get(\"/admin/\")\n154     assert response.status_code == 401\n155 \n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 154, "line_range": [154], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "161     # Should return 200 with empty results (no auth required for listing)\n162     assert response.status_code == 200\n163 \n", "col_offset": 4, "end_col_offset": 38, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 162, "line_range": [162], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "164     data = response.json()\n165     assert \"count\" in data\n166     assert \"results\" in data\n", "col_offset": 4, "end_col_offset": 26, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 165, "line_range": [165], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "165     assert \"count\" in data\n166     assert \"results\" in data\n167 \n", "col_offset": 4, "end_col_offset": 28, "filename": "fabiplus/tests/test_basic.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 166, "line_range": [166], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "42 \n43             assert result.exit_code == 0\n44             mock_template.assert_called_once_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 43, "line_range": [43], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "69 \n70             assert result.exit_code == 0\n71             mock_template.assert_called_once_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 70, "line_range": [70], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "87 \n88             assert result.exit_code == 0\n89             mock_template.assert_called_once_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 88, "line_range": [88], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "105 \n106             assert result.exit_code == 0\n107             mock_instance.create_project.assert_called_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 106, "line_range": [106], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "114 \n115         assert result.exit_code == 0\n116         assert \"Available Project Templates:\" in result.stdout\n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 115, "line_range": [115], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "115         assert result.exit_code == 0\n116         assert \"Available Project Templates:\" in result.stdout\n117         assert \"default\" in result.stdout\n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "116         assert \"Available Project Templates:\" in result.stdout\n117         assert \"default\" in result.stdout\n118         assert \"minimal\" in result.stdout\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 117, "line_range": [117], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "117         assert \"default\" in result.stdout\n118         assert \"minimal\" in result.stdout\n119         assert \"full\" in result.stdout\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 118, "line_range": [118], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "118         assert \"minimal\" in result.stdout\n119         assert \"full\" in result.stdout\n120         assert \"microservice\" in result.stdout\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 119, "line_range": [119], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "119         assert \"full\" in result.stdout\n120         assert \"microservice\" in result.stdout\n121         assert \"monolith\" in result.stdout\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 120, "line_range": [120], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "120         assert \"microservice\" in result.stdout\n121         assert \"monolith\" in result.stdout\n122 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 121, "line_range": [121], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "132 \n133             assert result.exit_code == 0\n134             mock_instance.init_existing_project.assert_called_once()\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 133, "line_range": [133], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "148 \n149             assert result.exit_code == 1\n150             assert \"Error creating project\" in result.stdout\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 149, "line_range": [149], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "149             assert result.exit_code == 1\n150             assert \"Error creating project\" in result.stdout\n151 \n", "col_offset": 12, "end_col_offset": 60, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "181 \n182             assert result.exit_code == 0\n183             mock_template.assert_called_once_with(app_name, \"default\")\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 182, "line_range": [182], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "202 \n203             assert result.exit_code == 0\n204             mock_template.assert_called_once_with(app_name, template_type)\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 203, "line_range": [203], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "218 \n219             assert result.exit_code == 0\n220             mock_instance.create_app.assert_called_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 219, "line_range": [219], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "239 \n240             assert result.exit_code == 0\n241             mock_instance.create_app.assert_called_with(\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 240, "line_range": [240], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "248 \n249         assert result.exit_code == 0\n250         assert \"Available App Templates:\" in result.stdout\n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 249, "line_range": [249], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "249         assert result.exit_code == 0\n250         assert \"Available App Templates:\" in result.stdout\n251         assert \"default\" in result.stdout\n", "col_offset": 8, "end_col_offset": 58, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 250, "line_range": [250], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "250         assert \"Available App Templates:\" in result.stdout\n251         assert \"default\" in result.stdout\n252         assert \"minimal\" in result.stdout\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 251, "line_range": [251], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "251         assert \"default\" in result.stdout\n252         assert \"minimal\" in result.stdout\n253         assert \"api\" in result.stdout\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 252, "line_range": [252], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "252         assert \"minimal\" in result.stdout\n253         assert \"api\" in result.stdout\n254         assert \"crud\" in result.stdout\n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 253, "line_range": [253], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "253         assert \"api\" in result.stdout\n254         assert \"crud\" in result.stdout\n255         assert \"readonly\" in result.stdout\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 254, "line_range": [254], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "254         assert \"crud\" in result.stdout\n255         assert \"readonly\" in result.stdout\n256         assert \"auth\" in result.stdout\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 255, "line_range": [255], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "255         assert \"readonly\" in result.stdout\n256         assert \"auth\" in result.stdout\n257         assert \"blog\" in result.stdout\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 256, "line_range": [256], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "256         assert \"auth\" in result.stdout\n257         assert \"blog\" in result.stdout\n258         assert \"ecommerce\" in result.stdout\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 257, "line_range": [257], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "257         assert \"blog\" in result.stdout\n258         assert \"ecommerce\" in result.stdout\n259 \n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 258, "line_range": [258], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "275 \n276             assert result.exit_code == 0\n277             # Check that the model was added to the file\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 276, "line_range": [276], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "278             models_content = (apps_dir / \"models.py\").read_text()\n279             assert f\"class {model_name}\" in models_content\n280 \n", "col_offset": 12, "end_col_offset": 58, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 279, "line_range": [279], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "294 \n295         assert result.exit_code == 0\n296         assert not app_dir.exists()\n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 295, "line_range": [295], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "295         assert result.exit_code == 0\n296         assert not app_dir.exists()\n297 \n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "315 \n316         assert result.exit_code == 0\n317         assert \"app1\" in result.stdout\n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 316, "line_range": [316], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "316         assert result.exit_code == 0\n317         assert \"app1\" in result.stdout\n318         assert \"app2\" in result.stdout\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 317, "line_range": [317], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "317         assert \"app1\" in result.stdout\n318         assert \"app2\" in result.stdout\n319 \n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 318, "line_range": [318], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "334 \n335             assert result.exit_code == 1\n336             assert \"Error creating app\" in result.stdout\n", "col_offset": 12, "end_col_offset": 40, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 335, "line_range": [335], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "335             assert result.exit_code == 1\n336             assert \"Error creating app\" in result.stdout\n337 \n", "col_offset": 12, "end_col_offset": 56, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 336, "line_range": [336], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "361 \n362         assert result.exit_code == 0\n363 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 362, "line_range": [362], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "364         project_dir = self.temp_dir / project_name\n365         assert project_dir.exists()\n366         assert (project_dir / \"pyproject.toml\").exists()\n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 365, "line_range": [365], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "365         assert project_dir.exists()\n366         assert (project_dir / \"pyproject.toml\").exists()\n367         assert (project_dir / \"manage.py\").exists()\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 366, "line_range": [366], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "366         assert (project_dir / \"pyproject.toml\").exists()\n367         assert (project_dir / \"manage.py\").exists()\n368         assert (project_dir / project_name / \"settings.py\").exists()\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 367, "line_range": [367], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "367         assert (project_dir / \"manage.py\").exists()\n368         assert (project_dir / project_name / \"settings.py\").exists()\n369 \n", "col_offset": 8, "end_col_offset": 68, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 368, "line_range": [368], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "379 \n380         assert result.exit_code == 0\n381 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 380, "line_range": [380], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "385         pyproject_content = (project_dir / \"pyproject.toml\").read_text()\n386         assert f'name = \"{project_name}\"' in pyproject_content\n387         # Check for FastAPI and other dependencies instead of fabiplus\n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 386, "line_range": [386], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "387         # Check for FastAPI and other dependencies instead of fabiplus\n388         assert \"fastapi\" in pyproject_content\n389 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 388, "line_range": [388], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "391         settings_content = (project_dir / project_name / \"settings.py\").read_text()\n392         assert \"from fabiplus.conf.settings import *\" in settings_content\n393         assert f'APP_NAME = \"{project_name.title()} API\"' in settings_content\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 392, "line_range": [392], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "392         assert \"from fabiplus.conf.settings import *\" in settings_content\n393         assert f'APP_NAME = \"{project_name.title()} API\"' in settings_content\n394 \n", "col_offset": 8, "end_col_offset": 77, "filename": "fabiplus/tests/test_cli_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 393, "line_range": [393], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "43         \"\"\"Test storage backend initialization\"\"\"\n44         assert self.storage.config.base_path == str(self.temp_dir)\n45         assert self.temp_dir.exists()\n", "col_offset": 8, "end_col_offset": 66, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 44, "line_range": [44], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "44         assert self.storage.config.base_path == str(self.temp_dir)\n45         assert self.temp_dir.exists()\n46 \n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 45, "line_range": [45], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "55 \n56         assert isinstance(metadata, FileMetadata)\n57         assert metadata.original_filename == \"test.txt\"\n", "col_offset": 8, "end_col_offset": 49, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 56, "line_range": [56], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "56         assert isinstance(metadata, FileMetadata)\n57         assert metadata.original_filename == \"test.txt\"\n58         assert metadata.size == len(test_content)\n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 57, "line_range": [57], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "57         assert metadata.original_filename == \"test.txt\"\n58         assert metadata.size == len(test_content)\n59         assert metadata.content_type == \"text/plain\"\n", "col_offset": 8, "end_col_offset": 49, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 58, "line_range": [58], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "58         assert metadata.size == len(test_content)\n59         assert metadata.content_type == \"text/plain\"\n60 \n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "61         # Check file exists\n62         assert await self.storage.exists(metadata.path)\n63 \n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 62, "line_range": [62], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "65         retrieved_metadata = await self.storage.get_metadata(metadata.path)\n66         assert retrieved_metadata is not None\n67         assert retrieved_metadata.size == metadata.size\n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 66, "line_range": [66], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "66         assert retrieved_metadata is not None\n67         assert retrieved_metadata.size == metadata.size\n68 \n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 67, "line_range": [67], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75         # Verify file exists\n76         assert await self.storage.exists(metadata.path)\n77 \n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "79         deleted = await self.storage.delete(metadata.path)\n80         assert deleted is True\n81 \n", "col_offset": 8, "end_col_offset": 30, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 80, "line_range": [80], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "82         # Verify file no longer exists\n83         assert not await self.storage.exists(metadata.path)\n84 \n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 83, "line_range": [83], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "93         files = await self.storage.list_files()\n94         assert len(files) == 3\n95 \n", "col_offset": 8, "end_col_offset": 30, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 94, "line_range": [94], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "97         files_page1 = await self.storage.list_files(limit=2, offset=0)\n98         assert len(files_page1) == 2\n99 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 98, "line_range": [98], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "100         files_page2 = await self.storage.list_files(limit=2, offset=2)\n101         assert len(files_page2) == 1\n102 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "110 \n111         assert validator.max_file_size == 1024\n112         assert \".jpg\" in validator.allowed_extensions\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 111, "line_range": [111], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "111         assert validator.max_file_size == 1024\n112         assert \".jpg\" in validator.allowed_extensions\n113 \n", "col_offset": 8, "end_col_offset": 53, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 112, "line_range": [112], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "140         result = await validator.validate(mock_file)\n141         assert result[\"valid\"] is True\n142 \n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 141, "line_range": [141], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "146 \n147         assert validator.max_width == 1920\n148         assert validator.max_height == 1080\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 147, "line_range": [147], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "147         assert validator.max_width == 1920\n148         assert validator.max_height == 1080\n149         assert \".jpg\" in validator.allowed_extensions\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 148, "line_range": [148], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "148         assert validator.max_height == 1080\n149         assert \".jpg\" in validator.allowed_extensions\n150         assert \"image/jpeg\" in validator.allowed_mime_types\n", "col_offset": 8, "end_col_offset": 53, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 149, "line_range": [149], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "149         assert \".jpg\" in validator.allowed_extensions\n150         assert \"image/jpeg\" in validator.allowed_mime_types\n151 \n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "155 \n156         assert \".pdf\" in validator.allowed_extensions\n157         assert \"application/pdf\" in validator.allowed_mime_types\n", "col_offset": 8, "end_col_offset": 53, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 156, "line_range": [156], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "156         assert \".pdf\" in validator.allowed_extensions\n157         assert \"application/pdf\" in validator.allowed_mime_types\n158 \n", "col_offset": 8, "end_col_offset": 64, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 157, "line_range": [157], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "167 \n168         assert processor.thumbnail_sizes == [(100, 100), (200, 200)]\n169 \n", "col_offset": 8, "end_col_offset": 68, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 168, "line_range": [168], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "196 \n197         assert \"width\" in result\n198         assert \"height\" in result\n", "col_offset": 8, "end_col_offset": 32, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 197, "line_range": [197], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "197         assert \"width\" in result\n198         assert \"height\" in result\n199         assert result[\"width\"] == 800\n", "col_offset": 8, "end_col_offset": 33, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 198, "line_range": [198], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "198         assert \"height\" in result\n199         assert result[\"width\"] == 800\n200         assert result[\"height\"] == 600\n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 199, "line_range": [199], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "199         assert result[\"width\"] == 800\n200         assert result[\"height\"] == 600\n201         assert \"thumbnails\" in result\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 200, "line_range": [200], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "200         assert result[\"height\"] == 600\n201         assert \"thumbnails\" in result\n202 \n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 201, "line_range": [201], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "244 \n245         assert media_file.original_filename == \"test.txt\"\n246         assert media_file.title == \"Test File\"\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 245, "line_range": [245], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "245         assert media_file.original_filename == \"test.txt\"\n246         assert media_file.title == \"Test File\"\n247         assert media_file.description == \"Test Description\"\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 246, "line_range": [246], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "246         assert media_file.title == \"Test File\"\n247         assert media_file.description == \"Test Description\"\n248         assert media_file.size == len(test_content)\n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 247, "line_range": [247], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "247         assert media_file.description == \"Test Description\"\n248         assert media_file.size == len(test_content)\n249 \n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 248, "line_range": [248], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "260 \n261         assert upload_session.filename == \"large_file.txt\"\n262         assert upload_session.total_size == 1000\n", "col_offset": 8, "end_col_offset": 58, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 261, "line_range": [261], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "261         assert upload_session.filename == \"large_file.txt\"\n262         assert upload_session.total_size == 1000\n263         assert upload_session.chunk_size == 100\n", "col_offset": 8, "end_col_offset": 48, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 262, "line_range": [262], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "262         assert upload_session.total_size == 1000\n263         assert upload_session.chunk_size == 100\n264         assert upload_session.total_chunks == 10\n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 263, "line_range": [263], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "263         assert upload_session.chunk_size == 100\n264         assert upload_session.total_chunks == 10\n265 \n", "col_offset": 8, "end_col_offset": 48, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 264, "line_range": [264], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "285 \n286         assert media_file.is_image is True\n287         assert media_file.is_video is False\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 286, "line_range": [286], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "286         assert media_file.is_image is True\n287         assert media_file.is_video is False\n288         assert media_file.is_audio is False\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 287, "line_range": [287], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "287         assert media_file.is_video is False\n288         assert media_file.is_audio is False\n289         assert media_file.is_document is False\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 288, "line_range": [288], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "288         assert media_file.is_audio is False\n289         assert media_file.is_document is False\n290 \n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 289, "line_range": [289], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "294 \n295         assert folder.name == \"Test Folder\"\n296         assert folder.slug == \"test-folder\"  # Auto-generated slug\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 295, "line_range": [295], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "295         assert folder.name == \"Test Folder\"\n296         assert folder.slug == \"test-folder\"  # Auto-generated slug\n297         assert folder.description == \"Test folder description\"\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "296         assert folder.slug == \"test-folder\"  # Auto-generated slug\n297         assert folder.description == \"Test folder description\"\n298 \n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "314 \n315         assert video_file.is_video is True\n316         assert video_file.is_image is False\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 315, "line_range": [315], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "315         assert video_file.is_video is True\n316         assert video_file.is_image is False\n317 \n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 316, "line_range": [316], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "331 \n332         assert pdf_file.is_document is True\n333         assert pdf_file.is_image is False\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 332, "line_range": [332], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "332         assert pdf_file.is_document is True\n333         assert pdf_file.is_image is False\n334 \n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_media_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 333, "line_range": [333], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "28         backends = ORMRegistry.list_backends()\n29         assert isinstance(backends, list)\n30         assert len(backends) > 0\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 29, "line_range": [29], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "29         assert isinstance(backends, list)\n30         assert len(backends) > 0\n31         assert \"sqlmodel\" in backends\n", "col_offset": 8, "end_col_offset": 32, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 30, "line_range": [30], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "30         assert len(backends) > 0\n31         assert \"sqlmodel\" in backends\n32         assert \"sqlalchemy\" in backends\n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 31, "line_range": [31], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "31         assert \"sqlmodel\" in backends\n32         assert \"sqlalchemy\" in backends\n33         assert \"tortoise\" in backends\n", "col_offset": 8, "end_col_offset": 39, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 32, "line_range": [32], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "32         assert \"sqlalchemy\" in backends\n33         assert \"tortoise\" in backends\n34 \n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "40         sqlmodel_backend = ORMRegistry.get_backend(\"sqlmodel\")\n41         assert sqlmodel_backend is not None\n42 \n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 41, "line_range": [41], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "43         sqlalchemy_backend = ORMRegistry.get_backend(\"sqlalchemy\")\n44         assert sqlalchemy_backend is not None\n45 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 44, "line_range": [44], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "46         tortoise_backend = ORMRegistry.get_backend(\"tortoise\")\n47         assert tortoise_backend is not None\n48 \n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 47, "line_range": [47], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "53         # Test valid backends\n54         assert ORMRegistry.validate_backend(\"sqlmodel\") is True\n55         assert ORMRegistry.validate_backend(\"sqlalchemy\") is True\n", "col_offset": 8, "end_col_offset": 63, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 54, "line_range": [54], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "54         assert ORMRegistry.validate_backend(\"sqlmodel\") is True\n55         assert ORMRegistry.validate_backend(\"sqlalchemy\") is True\n56         assert ORMRegistry.validate_backend(\"tortoise\") is True\n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 55, "line_range": [55], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "55         assert ORMRegistry.validate_backend(\"sqlalchemy\") is True\n56         assert ORMRegistry.validate_backend(\"tortoise\") is True\n57 \n", "col_offset": 8, "end_col_offset": 63, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 56, "line_range": [56], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "58         # Test invalid backend\n59         assert ORMRegistry.validate_backend(\"nonexistent\") is False\n60 \n", "col_offset": 8, "end_col_offset": 67, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "66         info = ORMRegistry.get_backend_info(\"sqlmodel\")\n67         assert info[\"name\"] == \"sqlmodel\"\n68         assert \"dependencies\" in info\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 67, "line_range": [67], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "67         assert info[\"name\"] == \"sqlmodel\"\n68         assert \"dependencies\" in info\n69         assert \"supports_async\" in info\n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 68, "line_range": [68], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "68         assert \"dependencies\" in info\n69         assert \"supports_async\" in info\n70         assert info[\"supports_async\"] is True\n", "col_offset": 8, "end_col_offset": 39, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 69, "line_range": [69], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "69         assert \"supports_async\" in info\n70         assert info[\"supports_async\"] is True\n71 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 70, "line_range": [70], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73         info = ORMRegistry.get_backend_info(\"tortoise\")\n74         assert info[\"name\"] == \"tortoise\"\n75         assert info[\"supports_async\"] is True\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74         assert info[\"name\"] == \"tortoise\"\n75         assert info[\"supports_async\"] is True\n76 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_orm_choice.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "58 \n59         assert perm.name == \"test_permission\"\n60         assert perm.scope == PermissionScope.MODEL\n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "59         assert perm.name == \"test_permission\"\n60         assert perm.scope == PermissionScope.MODEL\n61         assert perm.action == PermissionAction.READ\n", "col_offset": 8, "end_col_offset": 50, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 60, "line_range": [60], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60         assert perm.scope == PermissionScope.MODEL\n61         assert perm.action == PermissionAction.READ\n62         assert perm.resource == \"TestModel\"\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "61         assert perm.action == PermissionAction.READ\n62         assert perm.resource == \"TestModel\"\n63         assert perm.id is not None\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 62, "line_range": [62], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "62         assert perm.resource == \"TestModel\"\n63         assert perm.id is not None\n64 \n", "col_offset": 8, "end_col_offset": 34, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 63, "line_range": [63], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75         # Should match exact action and resource\n76         assert perm.matches(\"read\", \"User\") is True\n77 \n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "78         # Should not match different action\n79         assert perm.matches(\"write\", \"User\") is False\n80 \n", "col_offset": 8, "end_col_offset": 53, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 79, "line_range": [79], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "81         # Should not match different resource\n82         assert perm.matches(\"read\", \"Post\") is False\n83 \n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 82, "line_range": [82], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "91 \n92         assert admin_perm.matches(\"read\", \"User\") is True\n93         assert admin_perm.matches(\"write\", \"User\") is True\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 92, "line_range": [92], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "92         assert admin_perm.matches(\"read\", \"User\") is True\n93         assert admin_perm.matches(\"write\", \"User\") is True\n94         assert admin_perm.matches(\"delete\", \"User\") is True\n", "col_offset": 8, "end_col_offset": 58, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 93, "line_range": [93], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "93         assert admin_perm.matches(\"write\", \"User\") is True\n94         assert admin_perm.matches(\"delete\", \"User\") is True\n95 \n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 94, "line_range": [94], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "107         # Should match when condition is met\n108         assert perm.matches(\"read\", \"User\", department=\"engineering\") is True\n109 \n", "col_offset": 8, "end_col_offset": 77, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 108, "line_range": [108], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "110         # Should not match when condition is not met\n111         assert perm.matches(\"read\", \"User\", department=\"sales\") is False\n112 \n", "col_offset": 8, "end_col_offset": 72, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 111, "line_range": [111], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "113         # Should not match when condition is missing\n114         assert perm.matches(\"read\", \"User\") is False\n115 \n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 114, "line_range": [114], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "128         exception = exc_info.value\n129         assert str(exception) == \"Access denied\"\n130         assert exception.required_permission == \"read:users\"\n", "col_offset": 8, "end_col_offset": 48, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 129, "line_range": [129], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "129         assert str(exception) == \"Access denied\"\n130         assert exception.required_permission == \"read:users\"\n131         assert exception.user_id == \"user123\"\n", "col_offset": 8, "end_col_offset": 60, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "130         assert exception.required_permission == \"read:users\"\n131         assert exception.user_id == \"user123\"\n132         assert exception.resource == \"User\"\n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 131, "line_range": [131], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "131         assert exception.user_id == \"user123\"\n132         assert exception.resource == \"User\"\n133         assert exception.action == \"read\"\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 132, "line_range": [132], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "132         assert exception.resource == \"User\"\n133         assert exception.action == \"read\"\n134 \n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 133, "line_range": [133], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "155         retrieved = self.registry.get_permission(perm.id)\n156         assert retrieved == perm\n157 \n", "col_offset": 8, "end_col_offset": 32, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 156, "line_range": [156], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "187         read_perms = self.registry.list_permissions(action=PermissionAction.READ)\n188         assert len(read_perms) == 2\n189 \n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 188, "line_range": [188], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "191         user_perms = self.registry.list_permissions(resource=\"User\")\n192         assert len(user_perms) == 2\n193 \n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 192, "line_range": [192], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "195         model_perms = self.registry.list_permissions(scope=PermissionScope.MODEL)\n196         assert len(model_perms) == 3\n197 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 196, "line_range": [196], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "221 \n222         assert len(perm_set) == 2\n223         assert perm_set.has_permission(\"read\", \"User\") is True\n", "col_offset": 8, "end_col_offset": 33, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 222, "line_range": [222], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "222         assert len(perm_set) == 2\n223         assert perm_set.has_permission(\"read\", \"User\") is True\n224         assert perm_set.has_permission(\"update\", \"User\") is True\n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 223, "line_range": [223], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "223         assert perm_set.has_permission(\"read\", \"User\") is True\n224         assert perm_set.has_permission(\"update\", \"User\") is True\n225         assert perm_set.has_permission(\"delete\", \"User\") is False\n", "col_offset": 8, "end_col_offset": 64, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 224, "line_range": [224], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "224         assert perm_set.has_permission(\"update\", \"User\") is True\n225         assert perm_set.has_permission(\"delete\", \"User\") is False\n226 \n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 225, "line_range": [225], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "240         perm_set.add_permission(perm)\n241         assert len(perm_set) == 1\n242         assert perm_set.has_permission(\"read\", \"Test\") is True\n", "col_offset": 8, "end_col_offset": 33, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 241, "line_range": [241], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "241         assert len(perm_set) == 1\n242         assert perm_set.has_permission(\"read\", \"Test\") is True\n243 \n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 242, "line_range": [242], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "245         perm_set.remove_permission(perm.id)\n246         assert len(perm_set) == 0\n247         assert perm_set.has_permission(\"read\", \"Test\") is False\n", "col_offset": 8, "end_col_offset": 33, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 246, "line_range": [246], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "246         assert len(perm_set) == 0\n247         assert perm_set.has_permission(\"read\", \"Test\") is False\n248 \n", "col_offset": 8, "end_col_offset": 63, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 247, "line_range": [247], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "263 \n264         assert user_perm.is_valid is True\n265         assert user_perm.is_expired is False\n", "col_offset": 8, "end_col_offset": 41, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 264, "line_range": [264], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "264         assert user_perm.is_valid is True\n265         assert user_perm.is_expired is False\n266 \n", "col_offset": 8, "end_col_offset": 44, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 265, "line_range": [265], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "268         user_perm.expires_at = datetime.now() - timed<PERSON><PERSON>(hours=1)\n269         assert user_perm.is_expired is True\n270         assert user_perm.is_valid is False\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "269         assert user_perm.is_expired is True\n270         assert user_perm.is_valid is False\n271 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 270, "line_range": [270], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "282 \n283         assert model_perm.has_permission(PermissionAction.READ) is True\n284         assert model_perm.has_permission(PermissionAction.UPDATE) is True\n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 283, "line_range": [283], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "283         assert model_perm.has_permission(PermissionAction.READ) is True\n284         assert model_perm.has_permission(PermissionAction.UPDATE) is True\n285         assert model_perm.has_permission(PermissionAction.DELETE) is False\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 284, "line_range": [284], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "284         assert model_perm.has_permission(PermissionAction.UPDATE) is True\n285         assert model_perm.has_permission(PermissionAction.DELETE) is False\n286 \n", "col_offset": 8, "end_col_offset": 74, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 285, "line_range": [285], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "288         model_perm.can_admin = True\n289         assert model_perm.has_permission(PermissionAction.DELETE) is True\n290 \n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 289, "line_range": [289], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "302 \n303         assert field_perm.has_permission(PermissionAction.READ) is True\n304         assert field_perm.has_permission(PermissionAction.UPDATE) is False\n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 303, "line_range": [303], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "303         assert field_perm.has_permission(PermissionAction.READ) is True\n304         assert field_perm.has_permission(PermissionAction.UPDATE) is False\n305         assert field_perm.is_sensitive is True\n", "col_offset": 8, "end_col_offset": 74, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 304, "line_range": [304], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "304         assert field_perm.has_permission(PermissionAction.UPDATE) is False\n305         assert field_perm.is_sensitive is True\n306 \n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 305, "line_range": [305], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "318 \n319         assert row_perm.has_permission(PermissionAction.READ) is True\n320         assert (\n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 319, "line_range": [319], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "319         assert row_perm.has_permission(PermissionAction.READ) is True\n320         assert (\n321             row_perm.has_permission(PermissionAction.UPDATE) is True\n322         )  # Owner can update\n323         assert (\n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 320, "line_range": [320, 321, 322], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "322         )  # Owner can update\n323         assert (\n324             row_perm.has_permission(PermissionAction.DELETE) is True\n325         )  # Owner can delete\n326 \n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 323, "line_range": [323, 324, 325], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "367             result = checker.check_permission(self.user, read_perm)\n368             assert result is True\n369 \n", "col_offset": 12, "end_col_offset": 33, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 368, "line_range": [368], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "375         # Test that it routes to appropriate sub-checkers\n376         assert isinstance(checker.model_checker, ModelPermissionChecker)\n377         assert isinstance(checker.field_checker, FieldPermissionChecker)\n", "col_offset": 8, "end_col_offset": 72, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 376, "line_range": [376], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "376         assert isinstance(checker.model_checker, ModelPermissionChecker)\n377         assert isinstance(checker.field_checker, FieldPermissionChecker)\n378         assert isinstance(checker.row_checker, RowPermissionChecker)\n", "col_offset": 8, "end_col_offset": 72, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 377, "line_range": [377], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "377         assert isinstance(checker.field_checker, FieldPermissionChecker)\n378         assert isinstance(checker.row_checker, RowPermissionChecker)\n379 \n", "col_offset": 8, "end_col_offset": 68, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 378, "line_range": [378], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "406             # For now, just verify the decorator structure\n407             assert callable(test_endpoint)\n408 \n", "col_offset": 12, "end_col_offset": 42, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 407, "line_range": [407], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "415 \n416         assert callable(test_endpoint)\n417 \n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 416, "line_range": [416], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "424 \n425         assert callable(test_endpoint)\n426 \n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 425, "line_range": [425], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "479             result = checker.check_permission(self.user, model_permission)\n480             assert result is True\n481 \n", "col_offset": 12, "end_col_offset": 33, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 480, "line_range": [480], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "493         # Admin should have all permissions\n494         assert admin_perm.has_permission(PermissionAction.READ) is True\n495         assert admin_perm.has_permission(PermissionAction.UPDATE) is True\n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 494, "line_range": [494], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "494         assert admin_perm.has_permission(PermissionAction.READ) is True\n495         assert admin_perm.has_permission(PermissionAction.UPDATE) is True\n496         assert admin_perm.has_permission(PermissionAction.DELETE) is True\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 495, "line_range": [495], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "495         assert admin_perm.has_permission(PermissionAction.UPDATE) is True\n496         assert admin_perm.has_permission(PermissionAction.DELETE) is True\n497         assert admin_perm.has_permission(PermissionAction.CREATE) is True\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 496, "line_range": [496], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "496         assert admin_perm.has_permission(PermissionAction.DELETE) is True\n497         assert admin_perm.has_permission(PermissionAction.CREATE) is True\n498 \n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 497, "line_range": [497], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "512         # Owner should have all permissions regardless of explicit settings\n513         assert row_perm.has_permission(PermissionAction.READ) is True\n514         assert row_perm.has_permission(PermissionAction.UPDATE) is True\n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 513, "line_range": [513], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "513         assert row_perm.has_permission(PermissionAction.READ) is True\n514         assert row_perm.has_permission(PermissionAction.UPDATE) is True\n515         assert row_perm.has_permission(PermissionAction.DELETE) is True\n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 514, "line_range": [514], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "514         assert row_perm.has_permission(PermissionAction.UPDATE) is True\n515         assert row_perm.has_permission(PermissionAction.DELETE) is True\n516 \n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 515, "line_range": [515], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "537         # Test lookup performance\n538         assert perm_set.has_permission(\"read\", \"Model_5\") is True\n539         assert perm_set.has_permission(\"write\", \"Model_5\") is False\n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 538, "line_range": [538], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "538         assert perm_set.has_permission(\"read\", \"Model_5\") is True\n539         assert perm_set.has_permission(\"write\", \"Model_5\") is False\n540 \n", "col_offset": 8, "end_col_offset": 67, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 539, "line_range": [539], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "541         # Should be fast even with many permissions\n542         assert len(perm_set) == 1000\n543 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_permissions_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 542, "line_range": [542], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "50 \n51         assert response.media_type == \"application/json\"\n52         assert response.status_code == 200\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 51, "line_range": [51], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "51         assert response.media_type == \"application/json\"\n52         assert response.status_code == 200\n53 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 52, "line_range": [52], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73         parsed = json.loads(full_content)\n74         assert \"metadata\" in parsed\n75         assert \"data\" in parsed\n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74         assert \"metadata\" in parsed\n75         assert \"data\" in parsed\n76         assert len(parsed[\"data\"]) == 3\n", "col_offset": 8, "end_col_offset": 31, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75         assert \"data\" in parsed\n76         assert len(parsed[\"data\"]) == 3\n77         assert parsed[\"count\"] == 3\n", "col_offset": 8, "end_col_offset": 39, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "76         assert len(parsed[\"data\"]) == 3\n77         assert parsed[\"count\"] == 3\n78 \n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "89 \n90         assert response.media_type == \"text/csv\"\n91         assert \"attachment\" in response.headers[\"Content-Disposition\"]\n", "col_offset": 8, "end_col_offset": 48, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 90, "line_range": [90], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "90         assert response.media_type == \"text/csv\"\n91         assert \"attachment\" in response.headers[\"Content-Disposition\"]\n92 \n", "col_offset": 8, "end_col_offset": 70, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 91, "line_range": [91], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "112         # Should have header + 2 data rows\n113         assert len(lines) >= 2\n114         assert \"name\" in lines[0]  # Header\n", "col_offset": 8, "end_col_offset": 30, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 113, "line_range": [113], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "113         assert len(lines) >= 2\n114         assert \"name\" in lines[0]  # <PERSON><PERSON>\n115         assert \"<PERSON>\" in full_content\n", "col_offset": 8, "end_col_offset": 33, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 114, "line_range": [114], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "114         assert \"name\" in lines[0]  # <PERSON><PERSON>\n115         assert \"<PERSON>\" in full_content\n116         assert \"<PERSON>\" in full_content\n", "col_offset": 8, "end_col_offset": 38, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 115, "line_range": [115], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "115         assert \"<PERSON>\" in full_content\n116         assert \"<PERSON>\" in full_content\n117 \n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127 \n128         assert response.media_type == \"application/xml\"\n129 \n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "138 \n139         assert response.media_type == \"text/event-stream\"\n140         assert \"no-cache\" in response.headers[\"Cache-Control\"]\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 139, "line_range": [139], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "139         assert response.media_type == \"text/event-stream\"\n140         assert \"no-cache\" in response.headers[\"Cache-Control\"]\n141 \n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 140, "line_range": [140], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "153 \n154         assert response.media_type == \"application/json\"\n155 \n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 154, "line_range": [154], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "166 \n167         assert response.status_code == 200\n168 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 167, "line_range": [167], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "170         content = json.loads(response.body.decode())\n171         assert \"data\" in content\n172         assert \"metadata\" in content\n", "col_offset": 8, "end_col_offset": 32, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 171, "line_range": [171], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "171         assert \"data\" in content\n172         assert \"metadata\" in content\n173         assert content[\"metadata\"][\"total_pages\"] == 5\n", "col_offset": 8, "end_col_offset": 36, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 172, "line_range": [172], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "172         assert \"metadata\" in content\n173         assert content[\"metadata\"][\"total_pages\"] == 5\n174         assert content[\"metadata\"][\"has_next\"] is True\n", "col_offset": 8, "end_col_offset": 54, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 173, "line_range": [173], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "173         assert content[\"metadata\"][\"total_pages\"] == 5\n174         assert content[\"metadata\"][\"has_next\"] is True\n175 \n", "col_offset": 8, "end_col_offset": 54, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 174, "line_range": [174], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "185         content = json.loads(response.body.decode())\n186         assert content[\"metadata\"][\"next_cursor\"] == \"5\"\n187         assert content[\"metadata\"][\"has_next\"] is True\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 186, "line_range": [186], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "186         assert content[\"metadata\"][\"next_cursor\"] == \"5\"\n187         assert content[\"metadata\"][\"has_next\"] is True\n188 \n", "col_offset": 8, "end_col_offset": 54, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 187, "line_range": [187], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "198         content = json.loads(response.body.decode())\n199         assert content[\"pagination\"][\"last_id\"] == \"19\"\n200         assert content[\"pagination\"][\"has_more\"] is True\n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 199, "line_range": [199], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "199         assert content[\"pagination\"][\"last_id\"] == \"19\"\n200         assert content[\"pagination\"][\"has_more\"] is True\n201         assert content[\"pagination\"][\"count\"] == 20\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 200, "line_range": [200], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "200         assert content[\"pagination\"][\"has_more\"] is True\n201         assert content[\"pagination\"][\"count\"] == 20\n202 \n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 201, "line_range": [201], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "218 \n219         assert response.media_type == \"application/json\"\n220 \n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 219, "line_range": [219], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "234 \n235         assert (\n236             response.media_type\n237             == \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n238         )\n239         assert \"test.xlsx\" in response.headers[\"Content-Disposition\"]\n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 235, "line_range": [235, 236, 237, 238], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "238         )\n239         assert \"test.xlsx\" in response.headers[\"Content-Disposition\"]\n240 \n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 239, "line_range": [239], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "247 \n248         assert response.media_type == \"application/pdf\"\n249         assert \"test.pdf\" in response.headers[\"Content-Disposition\"]\n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 248, "line_range": [248], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "248         assert response.media_type == \"application/pdf\"\n249         assert \"test.pdf\" in response.headers[\"Content-Disposition\"]\n250 \n", "col_offset": 8, "end_col_offset": 68, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 249, "line_range": [249], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "263 \n264         assert response.media_type == \"image/png\"\n265 \n", "col_offset": 8, "end_col_offset": 49, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 264, "line_range": [264], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "276 \n277         assert response.media_type == \"application/zip\"\n278         assert \"archive.zip\" in response.headers[\"Content-Disposition\"]\n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 277, "line_range": [277], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "277         assert response.media_type == \"application/zip\"\n278         assert \"archive.zip\" in response.headers[\"Content-Disposition\"]\n279 \n", "col_offset": 8, "end_col_offset": 71, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 278, "line_range": [278], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "299 \n300         assert response.media_type == \"text/plain\"\n301         assert \"custom.txt\" in response.headers[\"Content-Disposition\"]\n", "col_offset": 8, "end_col_offset": 50, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 300, "line_range": [300], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "300         assert response.media_type == \"text/plain\"\n301         assert \"custom.txt\" in response.headers[\"Content-Disposition\"]\n302 \n", "col_offset": 8, "end_col_offset": 70, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 301, "line_range": [301], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "323         response = self.client.get(\"/stream-json\")\n324         assert response.status_code == 200\n325         assert response.headers[\"content-type\"] == \"application/json\"\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 324, "line_range": [324], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "324         assert response.status_code == 200\n325         assert response.headers[\"content-type\"] == \"application/json\"\n326 \n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 325, "line_range": [325], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "345         response = self.client.get(\"/paginated?page=2&per_page=5\")\n346         assert response.status_code == 200\n347 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 346, "line_range": [346], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "348         data = response.json()\n349         assert data[\"metadata\"][\"page\"] == 2\n350         assert data[\"metadata\"][\"per_page\"] == 5\n", "col_offset": 8, "end_col_offset": 44, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 349, "line_range": [349], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "349         assert data[\"metadata\"][\"page\"] == 2\n350         assert data[\"metadata\"][\"per_page\"] == 5\n351         assert len(data[\"data\"]) == 5\n", "col_offset": 8, "end_col_offset": 48, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 350, "line_range": [350], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "350         assert data[\"metadata\"][\"per_page\"] == 5\n351         assert len(data[\"data\"]) == 5\n352 \n", "col_offset": 8, "end_col_offset": 37, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 351, "line_range": [351], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "368         response = self.client.get(\"/export-csv\")\n369         assert response.status_code == 200\n370         assert response.headers[\"content-type\"] == \"text/csv\"\n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 369, "line_range": [369], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "369         assert response.status_code == 200\n370         assert response.headers[\"content-type\"] == \"text/csv\"\n371         assert \"users.csv\" in response.headers[\"content-disposition\"]\n", "col_offset": 8, "end_col_offset": 61, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 370, "line_range": [370], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "370         assert response.headers[\"content-type\"] == \"text/csv\"\n371         assert \"users.csv\" in response.headers[\"content-disposition\"]\n372 \n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 371, "line_range": [371], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "388         # Test that response is created quickly\n389         assert response.media_type == \"application/json\"\n390 \n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 389, "line_range": [389], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "397 \n398         assert chunk_count > 0\n399 \n", "col_offset": 8, "end_col_offset": 30, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 398, "line_range": [398], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "409 \n410         assert response.status_code == 200\n411 \n", "col_offset": 8, "end_col_offset": 42, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 410, "line_range": [410], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "413         content = json.loads(response.body.decode())\n414         assert len(content[\"data\"]) == 1000  # All data included\n415         assert content[\"metadata\"][\"total_items\"] == 10000\n", "col_offset": 8, "end_col_offset": 43, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 414, "line_range": [414], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "414         assert len(content[\"data\"]) == 1000  # All data included\n415         assert content[\"metadata\"][\"total_items\"] == 10000\n416 \n", "col_offset": 8, "end_col_offset": 58, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 415, "line_range": [415], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "435                 chunks.append(chunk)\n436         except Exception:\n437             pass  # Expected to fail\n438 \n", "col_offset": 8, "end_col_offset": 16, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 436, "line_range": [436, 437], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "439         # Should have gotten at least the first chunk\n440         assert len(chunks) > 0\n441 \n", "col_offset": 8, "end_col_offset": 30, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 440, "line_range": [440], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "450         # Should create response even with invalid data\n451         assert (\n452             response.media_type\n453             == \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n454         )\n455 \n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_response_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 451, "line_range": [451, 452, 453, 454], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "37 \n38         assert template.project_name == self.project_name\n39         assert template.template_type == \"default\"\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 38, "line_range": [38], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "38         assert template.project_name == self.project_name\n39         assert template.template_type == \"default\"\n40         assert template.include_docker is False\n", "col_offset": 8, "end_col_offset": 50, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 39, "line_range": [39], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "39         assert template.template_type == \"default\"\n40         assert template.include_docker is False\n41         assert template.jinja_env is not None\n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 40, "line_range": [40], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "40         assert template.include_docker is False\n41         assert template.jinja_env is not None\n42 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 41, "line_range": [41], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "48 \n49         assert template.include_docker is True\n50         assert template.template_type == \"full\"\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 49, "line_range": [49], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "49         assert template.include_docker is True\n50         assert template.template_type == \"full\"\n51 \n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 50, "line_range": [50], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "56 \n57         assert context[\"project_name\"] == self.project_name\n58         assert context[\"project_name_title\"] == self.project_name.title()\n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 57, "line_range": [57], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "57         assert context[\"project_name\"] == self.project_name\n58         assert context[\"project_name_title\"] == self.project_name.title()\n59         assert context[\"project_name_upper\"] == self.project_name.upper()\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 58, "line_range": [58], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "58         assert context[\"project_name_title\"] == self.project_name.title()\n59         assert context[\"project_name_upper\"] == self.project_name.upper()\n60         assert context[\"template_type\"] == \"default\"\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "59         assert context[\"project_name_upper\"] == self.project_name.upper()\n60         assert context[\"template_type\"] == \"default\"\n61         assert \"created_date\" in context\n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 60, "line_range": [60], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60         assert context[\"template_type\"] == \"default\"\n61         assert \"created_date\" in context\n62         assert \"created_year\" in context\n", "col_offset": 8, "end_col_offset": 40, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "61         assert \"created_date\" in context\n62         assert \"created_year\" in context\n63 \n", "col_offset": 8, "end_col_offset": 40, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 62, "line_range": [62], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74         # Check directories exist\n75         assert project_dir.exists()\n76         assert (project_dir / self.project_name).exists()\n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75         assert project_dir.exists()\n76         assert (project_dir / self.project_name).exists()\n77         assert (project_dir / \"apps\").exists()\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "76         assert (project_dir / self.project_name).exists()\n77         assert (project_dir / \"apps\").exists()\n78         assert (project_dir / \"tests\").exists()\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "77         assert (project_dir / \"apps\").exists()\n78         assert (project_dir / \"tests\").exists()\n79 \n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 78, "line_range": [78], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "80         # Check __init__.py files\n81         assert (project_dir / self.project_name / \"__init__.py\").exists()\n82         assert (project_dir / \"apps\" / \"__init__.py\").exists()\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 81, "line_range": [81], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "81         assert (project_dir / self.project_name / \"__init__.py\").exists()\n82         assert (project_dir / \"apps\" / \"__init__.py\").exists()\n83         assert (project_dir / \"tests\" / \"__init__.py\").exists()\n", "col_offset": 8, "end_col_offset": 62, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 82, "line_range": [82], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "82         assert (project_dir / \"apps\" / \"__init__.py\").exists()\n83         assert (project_dir / \"tests\" / \"__init__.py\").exists()\n84 \n", "col_offset": 8, "end_col_offset": 63, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 83, "line_range": [83], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "93         # Check essential files exist\n94         assert (project_dir / \"pyproject.toml\").exists()\n95         assert (project_dir / \"manage.py\").exists()\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 94, "line_range": [94], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "94         assert (project_dir / \"pyproject.toml\").exists()\n95         assert (project_dir / \"manage.py\").exists()\n96         assert (project_dir / \".env.example\").exists()\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 95, "line_range": [95], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "95         assert (project_dir / \"manage.py\").exists()\n96         assert (project_dir / \".env.example\").exists()\n97         assert (project_dir / \".gitignore\").exists()\n", "col_offset": 8, "end_col_offset": 54, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 96, "line_range": [96], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "96         assert (project_dir / \".env.example\").exists()\n97         assert (project_dir / \".gitignore\").exists()\n98         assert (project_dir / \"README.md\").exists()\n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 97, "line_range": [97], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "97         assert (project_dir / \".gitignore\").exists()\n98         assert (project_dir / \"README.md\").exists()\n99         assert (project_dir / self.project_name / \"settings.py\").exists()\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 98, "line_range": [98], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "98         assert (project_dir / \"README.md\").exists()\n99         assert (project_dir / self.project_name / \"settings.py\").exists()\n100         assert (project_dir / self.project_name / \"urls.py\").exists()\n", "col_offset": 8, "end_col_offset": 73, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 99, "line_range": [99], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "99         assert (project_dir / self.project_name / \"settings.py\").exists()\n100         assert (project_dir / self.project_name / \"urls.py\").exists()\n101         assert (project_dir / self.project_name / \"wsgi.py\").exists()\n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "100         assert (project_dir / self.project_name / \"urls.py\").exists()\n101         assert (project_dir / self.project_name / \"wsgi.py\").exists()\n102         assert (project_dir / self.project_name / \"asgi.py\").exists()\n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "101         assert (project_dir / self.project_name / \"wsgi.py\").exists()\n102         assert (project_dir / self.project_name / \"asgi.py\").exists()\n103 \n", "col_offset": 8, "end_col_offset": 69, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 102, "line_range": [102], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "112         # Check Docker files exist\n113         assert (project_dir / \"Dockerfile\").exists()\n114         assert (project_dir / \"docker-compose.yml\").exists()\n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 113, "line_range": [113], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "113         assert (project_dir / \"Dockerfile\").exists()\n114         assert (project_dir / \"docker-compose.yml\").exists()\n115         assert (project_dir / \".dockerignore\").exists()\n", "col_offset": 8, "end_col_offset": 60, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 114, "line_range": [114], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "114         assert (project_dir / \"docker-compose.yml\").exists()\n115         assert (project_dir / \".dockerignore\").exists()\n116 \n", "col_offset": 8, "end_col_offset": 55, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 115, "line_range": [115], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "125         # Check Docker files don't exist\n126         assert not (project_dir / \"Dockerfile\").exists()\n127         assert not (project_dir / \"docker-compose.yml\").exists()\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 126, "line_range": [126], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "126         assert not (project_dir / \"Dockerfile\").exists()\n127         assert not (project_dir / \"docker-compose.yml\").exists()\n128         assert not (project_dir / \".dockerignore\").exists()\n", "col_offset": 8, "end_col_offset": 64, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 127, "line_range": [127], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127         assert not (project_dir / \"docker-compose.yml\").exists()\n128         assert not (project_dir / \".dockerignore\").exists()\n129 \n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "139         settings_content = (project_dir / self.project_name / \"settings.py\").read_text()\n140         assert f'APP_NAME = \"{self.project_name.title()} API\"' in settings_content\n141 \n", "col_offset": 8, "end_col_offset": 82, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 140, "line_range": [140], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "143         readme_content = (project_dir / \"README.md\").read_text()\n144         assert f\"# {self.project_name.title()}\" in readme_content\n145 \n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 144, "line_range": [144], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "147         env_content = (project_dir / \".env.example\").read_text()\n148         assert f\"DATABASE_URL=sqlite:///./{self.project_name}.db\" in env_content\n149 \n", "col_offset": 8, "end_col_offset": 80, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 148, "line_range": [148], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "176         # Verify project structure\n177         assert project_dir.exists()\n178         assert (project_dir / self.project_name).exists()\n", "col_offset": 8, "end_col_offset": 35, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 177, "line_range": [177], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "177         assert project_dir.exists()\n178         assert (project_dir / self.project_name).exists()\n179         assert (project_dir / \"apps\").exists()\n", "col_offset": 8, "end_col_offset": 57, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 178, "line_range": [178], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "178         assert (project_dir / self.project_name).exists()\n179         assert (project_dir / \"apps\").exists()\n180         assert (project_dir / \"tests\").exists()\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 179, "line_range": [179], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "179         assert (project_dir / \"apps\").exists()\n180         assert (project_dir / \"tests\").exists()\n181 \n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 180, "line_range": [180], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "182         # Verify files\n183         assert (project_dir / \"pyproject.toml\").exists()\n184         assert (project_dir / \"manage.py\").exists()\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 183, "line_range": [183], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "183         assert (project_dir / \"pyproject.toml\").exists()\n184         assert (project_dir / \"manage.py\").exists()\n185         assert (project_dir / \"Dockerfile\").exists()  # Docker enabled\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 184, "line_range": [184], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "184         assert (project_dir / \"manage.py\").exists()\n185         assert (project_dir / \"Dockerfile\").exists()  # Docker enabled\n186 \n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 185, "line_range": [185], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "200         # Verify old content is removed and new project created\n201         assert not (project_dir / \"existing_file.txt\").exists()\n202         assert (project_dir / \"pyproject.toml\").exists()\n", "col_offset": 8, "end_col_offset": 63, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 201, "line_range": [201], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "201         assert not (project_dir / \"existing_file.txt\").exists()\n202         assert (project_dir / \"pyproject.toml\").exists()\n203 \n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 202, "line_range": [202], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "215         # Verify existing files are preserved\n216         assert (project_dir / \"existing.txt\").exists()\n217 \n", "col_offset": 8, "end_col_offset": 54, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 216, "line_range": [216], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "218         # Verify FABI+ files are added\n219         assert (project_dir / \"pyproject.toml\").exists()\n220         assert (project_dir / \"manage.py\").exists()\n", "col_offset": 8, "end_col_offset": 56, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 219, "line_range": [219], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "219         assert (project_dir / \"pyproject.toml\").exists()\n220         assert (project_dir / \"manage.py\").exists()\n221 \n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 220, "line_range": [220], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "239 \n240         assert template.app_name == self.app_name\n241         assert template.template_type == \"default\"\n", "col_offset": 8, "end_col_offset": 49, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 240, "line_range": [240], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "240         assert template.app_name == self.app_name\n241         assert template.template_type == \"default\"\n242         assert template.jinja_env is not None\n", "col_offset": 8, "end_col_offset": 50, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 241, "line_range": [241], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "241         assert template.template_type == \"default\"\n242         assert template.jinja_env is not None\n243 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 242, "line_range": [242], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "248 \n249         assert context[\"app_name\"] == self.app_name\n250         assert context[\"app_name_title\"] == self.app_name.title()\n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 249, "line_range": [249], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "249         assert context[\"app_name\"] == self.app_name\n250         assert context[\"app_name_title\"] == self.app_name.title()\n251         assert context[\"app_name_upper\"] == self.app_name.upper()\n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 250, "line_range": [250], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "250         assert context[\"app_name_title\"] == self.app_name.title()\n251         assert context[\"app_name_upper\"] == self.app_name.upper()\n252         assert context[\"template_type\"] == \"default\"\n", "col_offset": 8, "end_col_offset": 65, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 251, "line_range": [251], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "251         assert context[\"app_name_upper\"] == self.app_name.upper()\n252         assert context[\"template_type\"] == \"default\"\n253         assert \"created_date\" in context\n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 252, "line_range": [252], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "252         assert context[\"template_type\"] == \"default\"\n253         assert \"created_date\" in context\n254         assert \"created_year\" in context\n", "col_offset": 8, "end_col_offset": 40, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 253, "line_range": [253], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "253         assert \"created_date\" in context\n254         assert \"created_year\" in context\n255 \n", "col_offset": 8, "end_col_offset": 40, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 254, "line_range": [254], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "263         # Check all app files exist\n264         assert (app_dir / \"__init__.py\").exists()\n265         assert (app_dir / \"models.py\").exists()\n", "col_offset": 8, "end_col_offset": 49, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 264, "line_range": [264], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "264         assert (app_dir / \"__init__.py\").exists()\n265         assert (app_dir / \"models.py\").exists()\n266         assert (app_dir / \"views.py\").exists()\n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 265, "line_range": [265], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "265         assert (app_dir / \"models.py\").exists()\n266         assert (app_dir / \"views.py\").exists()\n267         assert (app_dir / \"admin.py\").exists()\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 266, "line_range": [266], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "266         assert (app_dir / \"views.py\").exists()\n267         assert (app_dir / \"admin.py\").exists()\n268         assert (app_dir / \"serializers.py\").exists()\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 267, "line_range": [267], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "267         assert (app_dir / \"admin.py\").exists()\n268         assert (app_dir / \"serializers.py\").exists()\n269         assert (app_dir / \"urls.py\").exists()\n", "col_offset": 8, "end_col_offset": 52, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 268, "line_range": [268], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "268         assert (app_dir / \"serializers.py\").exists()\n269         assert (app_dir / \"urls.py\").exists()\n270         assert (app_dir / \"tests.py\").exists()\n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "269         assert (app_dir / \"urls.py\").exists()\n270         assert (app_dir / \"tests.py\").exists()\n271         assert (app_dir / \"apps.py\").exists()\n", "col_offset": 8, "end_col_offset": 46, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 270, "line_range": [270], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "270         assert (app_dir / \"tests.py\").exists()\n271         assert (app_dir / \"apps.py\").exists()\n272 \n", "col_offset": 8, "end_col_offset": 45, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 271, "line_range": [271], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "281         models_content = (app_dir / \"models.py\").read_text()\n282         assert f\"{self.app_name.title()} Models\" in models_content\n283         assert (\n", "col_offset": 8, "end_col_offset": 66, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 282, "line_range": [282], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "282         assert f\"{self.app_name.title()} Models\" in models_content\n283         assert (\n284             \"from fabiplus.core.models import BaseModel, register_model\"\n285             in models_content\n286         )\n287 \n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 283, "line_range": [283, 284, 285, 286], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "289         views_content = (app_dir / \"views.py\").read_text()\n290         assert f\"{self.app_name.title()} Views\" in views_content\n291         assert (\n", "col_offset": 8, "end_col_offset": 64, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 290, "line_range": [290], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "290         assert f\"{self.app_name.title()} Views\" in views_content\n291         assert (\n292             f'router = APIRouter(prefix=\"/{self.app_name}\", tags=[\"{self.app_name.title()}\"])'\n293             in views_content\n294         )\n295 \n", "col_offset": 8, "end_col_offset": 9, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 291, "line_range": [291, 292, 293, 294], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "297         apps_content = (app_dir / \"apps.py\").read_text()\n298         assert f'name = \"{self.app_name}\"' in apps_content\n299         assert f'verbose_name = \"{self.app_name.title()}\"' in apps_content\n", "col_offset": 8, "end_col_offset": 58, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 298, "line_range": [298], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "298         assert f'name = \"{self.app_name}\"' in apps_content\n299         assert f'verbose_name = \"{self.app_name.title()}\"' in apps_content\n300 \n", "col_offset": 8, "end_col_offset": 74, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 299, "line_range": [299], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "313         # Verify old content is removed and new app created\n314         assert not (app_dir / \"existing_file.txt\").exists()\n315         assert (app_dir / \"models.py\").exists()\n", "col_offset": 8, "end_col_offset": 59, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 314, "line_range": [314], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "314         assert not (app_dir / \"existing_file.txt\").exists()\n315         assert (app_dir / \"models.py\").exists()\n316 \n", "col_offset": 8, "end_col_offset": 47, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 315, "line_range": [315], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "337         models_content = (app_dir / \"models.py\").read_text()\n338         assert \"class TestModel(BaseModel, table=True):\" in models_content\n339         assert \"@register_model\" in models_content\n", "col_offset": 8, "end_col_offset": 74, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 338, "line_range": [338], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "338         assert \"class TestModel(BaseModel, table=True):\" in models_content\n339         assert \"@register_model\" in models_content\n340 \n", "col_offset": 8, "end_col_offset": 50, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 339, "line_range": [339], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "342         admin_content = (app_dir / \"admin.py\").read_text()\n343         assert \"class TestModelAdmin(AdminView):\" in admin_content\n344         assert \"model = TestModel\" in admin_content\n", "col_offset": 8, "end_col_offset": 66, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 343, "line_range": [343], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "343         assert \"class TestModelAdmin(AdminView):\" in admin_content\n344         assert \"model = TestModel\" in admin_content\n345 \n", "col_offset": 8, "end_col_offset": 51, "filename": "fabiplus/tests/test_templates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 344, "line_range": [344], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}]}