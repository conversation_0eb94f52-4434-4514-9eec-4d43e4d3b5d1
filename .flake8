[flake8]
# Maximum line length to match Black
max-line-length = 88

# Select specific error codes to check
select = 
    E,   # pycodestyle errors
    W,   # pycodestyle warnings
    F,   # pyflakes
    C,   # mccabe complexity
    B,   # flake8-bugbear
    I,   # isort

# Ignore specific error codes that conflict with <PERSON> or are not critical
ignore =
    E203,
    E501,
    W503,
    W504,
    E231,
    E701,
    W291,
    W293,
    E402,
    F401,
    F811,
    F841,
    B008,
    D100,
    D101,
    D102,
    D103,
    D104,
    D105,
    D107

# Exclude directories and files
exclude = 
    .git,
    __pycache__,
    .pytest_cache,
    .mypy_cache,
    .tox,
    .venv,
    venv,
    build,
    dist,
    migrations,
    *.egg-info,
    .eggs,
    *.pyc,
    *.pyo,
    *.pyd,
    .Python,
    env,
    pip-log.txt,
    pip-delete-this-directory.txt,
    .coverage,
    .coverage.*,
    htmlcov,
    .DS_Store

# Maximum complexity for mccabe (increased for framework complexity)
max-complexity = 15

# Show source code for each error
show-source = True

# Show pep8 violation statistics
statistics = True

# Count errors and warnings
count = True

# Per-file ignores for specific patterns
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401,F403
    # CLI files can have complex argument parsing and B008 issues
    fabiplus/cli/commands/*.py:C901,B008
    # Template files can have complex logic
    fabiplus/templates/*.py:C901
    # Core framework files with necessary complexity
    fabiplus/core/app.py:C901
    fabiplus/admin/ui.py:C901
    fabiplus/admin/routes.py:C901
    fabiplus/core/migrations.py:C901
    fabiplus/core/orm/*.py:C901
    fabiplus/api/auto.py:C901
    fabiplus/conf/settings.py:C901
    # Test files can be less strict
    test_*.py:D,C901,B008
    tests/*.py:D,C901,B008
    # Configuration files
    settings.py:F401
    config.py:F401
    # Migration files
    migrations/*:E501,F401,F403,C901

# Enable specific plugins
enable-extensions = 
    B,  # flake8-bugbear

# Inline comments
inline-quotes = double

# Import order style (to work with isort)
import-order-style = pep8

# Application import names
application-import-names = fabiplus

# Docstring conventions
docstring-convention = google
