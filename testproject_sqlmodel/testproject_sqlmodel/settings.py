"""
Testproject_Sqlmodel Settings
FABI+ project configuration with SQLMODEL backend
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Project specific settings
APP_NAME = "Testproject_Sqlmodel API"
DEBUG = True

# ORM Backend Configuration
ORM_BACKEND = "sqlmodel"

"""
Database settings for SQLModel
Generated by FABI+ framework
"""

import os
from fabiplus.conf.settings import *

# Helper function for environment variables
def env(key, default=None):
    return os.getenv(key, default)

# Database Configuration
DATABASE_URL = env("DATABASE_URL", default="sqlite:///./testproject_sqlmodel.db")

# SQLModel specific settings
SQLMODEL_DATABASE_URL = DATABASE_URL
SQLALCHEMY_DATABASE_URL = DATABASE_URL

# Migration settings
ALEMBIC_CONFIG = {
    "script_location": "migrations",
    "file_template": "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s",
    "timezone": "UTC",
}

# Model discovery
MODEL_MODULES = [
    "apps.*.models",
]


# Installed Apps
INSTALLED_APPS = [
    "apps.core",
    # Add your apps here:
    # "apps.blog",
    # "apps.users",
    "apps.blog",
]

# API Configuration
API_PREFIX = "/api/v1"

# Admin Configuration
ADMIN_ENABLED = True
ADMIN_PREFIX = "/admin"

# Security
SECRET_KEY = "testproject_sqlmodel-dev-secret-key-change-in-production"

# CORS
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]