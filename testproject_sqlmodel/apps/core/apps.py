"""
Core App Configuration
Django-style app configuration for core
"""

from fabiplus.core.apps import AppConfig


class CoreConfig(AppConfig):
    """Configuration for core app"""
    
    name = "core"
    verbose_name = "Core"
    
    def ready(self):
        """App initialization"""
        # Import models to register them
        from . import models
        
        # Import admin to register admin views
        from . import admin
        
        # Any other app initialization code
        pass


# Default app config
default_app_config = "core.apps.CoreConfig"