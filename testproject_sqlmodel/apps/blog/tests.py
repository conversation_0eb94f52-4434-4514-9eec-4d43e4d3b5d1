"""
Blog Tests
Test cases for the blog app
"""

import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session, SQLModel, create_engine
from sqlmodel.pool import StaticPool

from fabiplus.core.app import create_app
from .models import BlogItem


@pytest.fixture(name="session")
def session_fixture():
    """Create test database session"""
    engine = create_engine(
        "sqlite://", 
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


@pytest.fixture(name="client")
def client_fixture(session: Session):
    """Create test client"""
    def get_session_override():
        return session

    app = create_app()
    
    from fabiplus.core.models import ModelRegistry
    app.dependency_overrides[ModelRegistry.get_session] = get_session_override
    
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()


def test_create_blog_item(session: Session):
    """Test creating BlogItem"""
    item = BlogItem(
        name="Test Item",
        description="Test description",
        is_active=True
    )
    session.add(item)
    session.commit()
    session.refresh(item)
    
    assert item.id is not None
    assert item.name == "Test Item"
    assert item.is_active is True


def test_blog_api_endpoints(client: TestClient):
    """Test blog API endpoints"""
    # Test custom endpoint
    response = client.get("/api/blog/custom/")
    assert response.status_code == 200
    
    data = response.json()
    assert "message" in data


def test_blog_stats_endpoint(client: TestClient):
    """Test blog stats endpoint"""
    response = client.get("/api/blog/stats/")
    assert response.status_code == 200
    
    data = response.json()
    assert "total_items" in data
    assert "active_items" in data


class TestBlogItem:
    """Test class for BlogItem model"""
    
    def test_model_creation(self, session: Session):
        """Test model creation"""
        item = BlogItem(name="Test")
        session.add(item)
        session.commit()
        
        assert item.id is not None
        assert str(item) == "Test"
    
    def test_model_fields(self):
        """Test model fields"""
        item = BlogItem(name="Test")
        
        assert hasattr(item, "name")
        assert hasattr(item, "description")
        assert hasattr(item, "is_active")
        assert hasattr(item, "created_at")
        assert hasattr(item, "updated_at")