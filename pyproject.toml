[tool.poetry]
name = "fabiplus"
version = "0.1.0"
description = "A production-ready, modular, extensible API-only Python framework"
authors = ["FABI+ Team <<EMAIL>>"]
readme = "README.md"
homepage = "https://github.com/fabiplus/fabiplus"
repository = "https://github.com/fabiplus/fabiplus"
documentation = "https://docs.fabiplus.dev"
keywords = ["fastapi", "api", "framework", "async", "sqlmodel", "admin"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Framework :: FastAPI",
]
packages = [{include = "fabiplus"}]

[tool.poetry.dependencies]
python = "^3.10"

# Core framework dependencies (minimal for CLI and templates)
typer = {version = "0.12.3", extras = ["all"]}
click = "8.1.7"
rich = "^13.7.0"
jinja2 = "^3.1.0"
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"

# For template validation and basic functionality
fastapi = "^0.104.0"
sqlmodel = "^0.0.14"
sqlalchemy = "^2.0.0"
alembic = "^1.13.0"

# Optional ORM support for framework (not required for generated projects)
tortoise-orm = {version = "^0.20.0", optional = true}

# Optional database drivers for testing
psycopg2-binary = {version = "^2.9.0", optional = true}
pymysql = {version = "^1.1.0", optional = true}
asyncpg = {version = "^0.29.0", optional = true}

# Optional caching for testing
redis = {version = "^5.0.0", optional = true}
hiredis = {version = "^2.2.0", optional = true}

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-xdist = "^3.3.0"
httpx = "^0.25.0"
requests = "^2.31.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
flake8-bugbear = "^23.12.2"
flake8-docstrings = "^1.7.0"
mypy = "^1.7.0"
pre-commit = "^3.6.0"
bandit = "^1.7.5"
safety = "^2.3.0"
coverage = "^7.3.0"

[tool.poetry.group.docs.dependencies]
mkdocs = "^1.5.0"
mkdocs-material = "^9.4.0"

[tool.poetry.extras]
# ORM support
tortoise = ["tortoise-orm", "asyncpg"]

# Database drivers
postgresql = ["psycopg2-binary"]
mysql = ["pymysql"]
asyncpg = ["asyncpg"]

# Caching
redis = ["redis", "hiredis"]

# Development and testing
dev = ["psycopg2-binary", "pymysql", "asyncpg", "redis", "hiredis", "tortoise-orm"]
all = ["psycopg2-binary", "pymysql", "asyncpg", "redis", "hiredis", "tortoise-orm"]

[tool.poetry.scripts]
fabiplus = "fabiplus.cli.main:main"
fabi = "fabiplus.cli.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "*/venv/*", "*/build/*", "*/dist/*"]
known_first_party = ["fabiplus"]
known_third_party = ["fastapi", "sqlmodel", "sqlalchemy", "pydantic", "alembic", "uvicorn", "typer", "click", "rich"]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "tortoise.*",
    "asyncpg.*",
    "psycopg2.*",
    "pymysql.*",
    "redis.*",
    "jose.*",
    "passlib.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["fabiplus"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/migrations/*",
    "*/build/*",
    "*/dist/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

# Bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", "build", "dist"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args for tests
