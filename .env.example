# FABI+ Environment Configuration Example
# Copy this file to .env and update the values for your environment

# Application Settings
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///./fabiplus.db
# For PostgreSQL: postgresql://user:password@localhost:5432/dbname
# For MySQL: mysql://user:password@localhost:3306/dbname

# JWT Authentication
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Security Settings
SECURITY_ENABLED=true
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Rate Limiting
RATE_LIMITING_ENABLED=true
RATE_LIMIT_PER_MINUTE=60

# Caching
CACHE_TYPE=memory
# For Redis: redis://localhost:6379/0
REDIS_URL=redis://localhost:6379/0

# Admin Settings
ADMIN_ROUTES_IN_DOCS=false

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/fabiplus.log

# Email Configuration (Optional)
EMAIL_BACKEND=console
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx

# API Settings
API_VERSION=v1
API_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc

# Production Settings (for production deployment)
# ENVIRONMENT=production
# DEBUG=false
# DATABASE_URL=**********************************/fabiplus_prod
# CORS_ORIGINS=["https://yourdomain.com"]
# LOG_LEVEL=WARNING
